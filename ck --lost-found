[33m78dcac7[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mmain[m[33m)[m HEAD@{0}: commit: modification pour l'import RH&Text generales
[33m80f7c9a[m HEAD@{1}: commit: suppression des pourcentage et du plus dans chiffre clés
[33m11c7ffe[m HEAD@{2}: pull origin main: Fast-forward
[33m3733fe2[m HEAD@{3}: pull origin main: Fast-forward
[33md065aef[m HEAD@{4}: commit: Ajout de la parti import des document Transport routier
[33m7e9934b[m HEAD@{5}: pull origin main: Fast-forward
[33md139e16[m HEAD@{6}: commit: module minification js et webp
[33mcaea3f2[m HEAD@{7}: commit: modification pour l'import des texts géneraux et rh
[33m7be4840[m HEAD@{8}: pull origin main: Fast-forward
[33m1ae73f9[m HEAD@{9}: commit: modification block banner actualiter support webp
[33mf905356[m HEAD@{10}: commit: correction bugs image e-services
[33m1f14957[m HEAD@{11}: commit: affichage du logo
[33m2f39b88[m HEAD@{12}: commit: modification global des blocs de homepage
[33maa64556[m HEAD@{13}: commit: Modification global twig e-services
[33m7358edc[m HEAD@{14}: commit: style d'image mode de transport
[33m3ecf4ff[m HEAD@{15}: commit: style d'image bloc mtl Tv HP
[33me43b4af[m HEAD@{16}: commit: modification style d'image banniére HP
[33m0e63c2e[m HEAD@{17}: commit: correction style d'image mtl actualiter bloc banner
[33m6b3e455[m HEAD@{18}: commit: modification etablissement
[33m96c90a3[m HEAD@{19}: pull origin main: Fast-forward
[33m09a7803[m HEAD@{20}: commit: modification bloc listing reglementation et etablissement
[33m724a88e[m HEAD@{21}: pull origin main: Fast-forward
[33m6c0fa30[m HEAD@{22}: commit: modification dansmodule import_reglementation
[33m976aca2[m HEAD@{23}: pull origin main: Fast-forward
[33md086ee9[m HEAD@{24}: pull origin main: Fast-forward
[33m2f84f4e[m HEAD@{25}: commit: module d'import reglementation premiere parti
[33m1db51e2[m HEAD@{26}: pull origin main: Fast-forward
[33m0c22088[m HEAD@{27}: pull origin main: Fast-forward
[33m0665f0c[m HEAD@{28}: commit: mise à jour image mtl tv
[33macbcf6c[m HEAD@{29}: commit: mise à jour image actualite
[33m3578914[m HEAD@{30}: commit: modification version admintoolbar
[33mb8ffc21[m HEAD@{31}: commit: mise à jour admin toolbar
[33m4b39bb6[m HEAD@{32}: commit: modification bloc decouvrir aussi
[33m9bb4f8f[m HEAD@{33}: commit: correction bug banner
[33mb6f5fe8[m HEAD@{34}: pull origin main: Fast-forward
[33m5665f49[m HEAD@{35}: pull origin main: Fast-forward
[33m2512718[m HEAD@{36}: commit: ajout module correction search_api_meilisearch
[33m46f4b8f[m HEAD@{37}: pull origin main: Fast-forward
[33m21aea2c[m HEAD@{38}: commit: module meilisearch
[33mfcbbcd6[m HEAD@{39}: pull origin main: Fast-forward
[33m5d4602f[m HEAD@{40}: pull origin main: Fast-forward
[33m12077ea[m HEAD@{41}: commit: Premiere version du module d'import de reglementation
[33m26d0597[m HEAD@{42}: pull origin main: Fast-forward
[33m8e4d8f2[m HEAD@{43}: commit: page user twig file
[33m3a682f1[m HEAD@{44}: commit: MAj drupal 11
[33m41f5f33[m HEAD@{45}: commit: support drupal 11 du module scraper_news
[33m3ed0e7c[m HEAD@{46}: pull origin main: Fast-forward
[33m928d4cd[m HEAD@{47}: commit: netoyage des module custom inutiles correction bug banniere page taxonomy
[33m80cab12[m HEAD@{48}: pull origin main: Fast-forward
[33mf04af32[m HEAD@{49}: commit: dump BDD
[33m82b4a68[m HEAD@{50}: pull origin main: Fast-forward
[33m1fc7210[m HEAD@{51}: pull origin main: Fast-forward
[33mbc4081f[m HEAD@{52}: commit: modification block projet secteur
[33m61abfca[m HEAD@{53}: commit: modification block projet secteur
[33m205bbf9[m HEAD@{54}: pull origin main: Fast-forward
[33m70a3521[m HEAD@{55}: commit: modification dans la configuration bloc autre actualités
[33m3b2fde3[m HEAD@{56}: pull origin main: Fast-forward
[33maa8d56f[m HEAD@{57}: pull origin main: Fast-forward
[33m585b596[m HEAD@{58}: commit: banner page 404
[33m7ae2516[m HEAD@{59}: commit: modification dans la configuration du bloc decouvrir
[33m9f40477[m HEAD@{60}: commit: modification dans detail e-service support plusieur secteurs
[33m517479c[m HEAD@{61}: pull origin main: Fast-forward
[33mb114f47[m HEAD@{62}: pull origin main: Fast-forward
[33m9887f04[m HEAD@{63}: commit: correction bug publication
[33m4911a40[m HEAD@{64}: commit: modif page 404
[33mf138219[m HEAD@{65}: commit: support traduction page 404
[33m108bc2b[m HEAD@{66}: commit: formatage global de certain fichier et modification twig
[33md48d7bb[m HEAD@{67}: commit: ajout du placeholder dans le search
[33mfe3af2a[m HEAD@{68}: commit: modification bloc infos secteur
[33m2219105[m HEAD@{69}: commit: ajustement globale du code
[33m2db1ffa[m HEAD@{70}: commit: modification de la structure globale cote header et banner
[33ma1cd614[m HEAD@{71}: pull origin main: Fast-forward
[33m42b499c[m HEAD@{72}: commit: newsletter Homepage
[33mbc8256e[m HEAD@{73}: commit: modification newsletter cote backend
[33m86b332e[m HEAD@{74}: pull origin main: Fast-forward
[33m84c2aa0[m HEAD@{75}: commit: modification node e-service
[33m9f11d20[m HEAD@{76}: pull origin main: Fast-forward
[33m334c7ea[m HEAD@{77}: commit: bloc infos secteur aviation civille marine marchande
[33ma7bd61a[m HEAD@{78}: pull origin main: Fast-forward
[33m187db43[m HEAD@{79}: commit: modification lien condition d'utilisation
[33mbf782ee[m HEAD@{80}: commit: modification formulair newsletter
[33m7db09bc[m HEAD@{81}: pull origin main: Fast-forward
[33m51165a1[m HEAD@{82}: commit: Condition d'utilisation footer
[33m3e1cf17[m HEAD@{83}: pull origin main: Fast-forward
[33m904eb46[m HEAD@{84}: commit: modification page présentation
[33m85c1dab[m HEAD@{85}: commit: modification page publication
[33me2c56b8[m HEAD@{86}: commit: partage listing e-service
[33m236703d[m HEAD@{87}: pull origin main: Fast-forward
[33m8138ec0[m HEAD@{88}: commit: modif listing e-service
[33ma9ba99b[m HEAD@{89}: commit: correction placeholder newsletter
[33m7cf330c[m HEAD@{90}: commit: bloc partage page detail
[33meb1bc54[m HEAD@{91}: commit: Modification partage
[33ma8af544[m HEAD@{92}: pull origin main: Merge made by the 'ort' strategy.
[33m0be8f7a[m HEAD@{93}: commit: Modification partage
[33m7ee8d81[m HEAD@{94}: commit: modification globale et bloc newsletter
[33m9512194[m HEAD@{95}: commit: modification boutton traduction
[33m132f3c0[m HEAD@{96}: pull origin main: Fast-forward
[33m930f9b0[m HEAD@{97}: commit: page 404
[33mf0375fc[m HEAD@{98}: pull origin main: Merge made by the 'ort' strategy.
[33mddb04e1[m HEAD@{99}: commit: Page 404
[33mc78a9a6[m HEAD@{100}: commit: modification du timeout du module scraping news
[33m98765a1[m HEAD@{101}: commit: modification scraper_news
[33m8c58212[m HEAD@{102}: commit: modification module actualites
[33m37ce0a9[m HEAD@{103}: pull origin main: Fast-forward
[33mc5f8419[m HEAD@{104}: commit: correction bug module scraper_news
[33m8b09880[m HEAD@{105}: pull origin main: Fast-forward
[33mf161355[m HEAD@{106}: pull origin main: Fast-forward
[33mb9f7aba[m HEAD@{107}: commit: Ajout du block autre etablissement
[33m196a34e[m HEAD@{108}: pull origin main: Merge made by the 'ort' strategy.
[33mbf28822[m HEAD@{109}: commit: block autre etablissement
[33m70a1e75[m HEAD@{110}: commit: Tout afficher on ENG
[33m7020b15[m HEAD@{111}: commit: page projets listing cote back
[33m55b1121[m HEAD@{112}: pull origin main: Fast-forward
[33m4069a42[m HEAD@{113}: commit: modification du boutton decouvrir pour le bloc secteur HP
[33m5be0939[m HEAD@{114}: commit: bloc decouvrire dans présentation secteur
[33mbee621c[m HEAD@{115}: commit: Page présentation de secteur cote back
[33m26f4ded[m HEAD@{116}: pull origin main: Fast-forward
[33m60e9dd2[m HEAD@{117}: commit: bloc decouvrir etablissement
[33m9919a97[m HEAD@{118}: pull origin main: Fast-forward
[33m9c1d074[m HEAD@{119}: commit: correction orthographe présentation secteur
[33m6e1f5cd[m HEAD@{120}: pull origin main: Fast-forward
[33m7c564f3[m HEAD@{121}: commit: Modification bloc découvrire
[33m080ba2f[