uuid: e5f6g7h8-i9j0-1234-efgh-56789abcdef0
langcode: fr
status: true
dependencies:
  config:
    - node.type.reglementation
    - node.type.projet
    - node.type.partenaire
    - taxonomy.vocabulary.modes_de_transport
  module:
    - node
    - taxonomy
    - media
    - system
id: direction_de_la_marine_marchande
label: 'Direction de la Marine Marchande'
weight: 3
is_admin: false
permissions:
  # Permissions générales d'accès
  - 'access content'
  - 'access administration pages'
  - 'access content overview'
  - 'view the administration theme'
  - 'access toolbar'
  - 'view published content'
  - 'view unpublished content'
  
  # Permissions générales d'édition de contenu
  - 'edit own content'
  - 'edit any content'
  
  # Permissions pour Réglementations
  - 'create reglementation content'
  - 'edit own reglementation content'
  - 'edit any reglementation content'
  - 'delete own reglementation content'
  - 'delete any reglementation content'
  - 'view reglementation revisions'
  - 'revert reglementation revisions'
  - 'delete reglementation revisions'
  
  # Permissions pour Projets
  - 'create projet content'
  - 'edit own projet content'
  - 'edit any projet content'
  - 'delete own projet content'
  - 'delete any projet content'
  - 'view projet revisions'
  - 'revert projet revisions'
  - 'delete projet revisions'
  
  # Permissions pour Partenaires
  - 'create partenaire content'
  - 'edit own partenaire content'
  - 'edit any partenaire content'
  - 'delete own partenaire content'
  - 'delete any partenaire content'
  - 'view partenaire revisions'
  - 'revert partenaire revisions'
  - 'delete partenaire revisions'
  
  # Permissions pour les médias et fichiers
  - 'create media'
  - 'edit own media'
  - 'edit any media'
  - 'delete own media'
  - 'delete any media'
  - 'view media'
  - 'upload files'
  
  # Permissions pour les taxonomies
  - 'edit terms in modes_de_transport'
  - 'delete terms in modes_de_transport'
  
  # Permissions pour les vues
  - 'access views'
  - 'use views exposed filters'
  
  # Permissions pour les fichiers
  - 'access files overview'
  - 'view files'
  
  # Permissions pour les révisions
  - 'view all revisions'
  - 'revert all revisions'
  
  # Permissions pour les opérations en masse
  - 'use bulk operations'
  - 'execute bulk operations' 