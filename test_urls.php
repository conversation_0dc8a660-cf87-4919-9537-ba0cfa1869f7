<?php

// Test des différentes URLs pour comprendre le problème

$urls_to_test = [
  'transport_routier_fr' => 'https://www.transport.gov.ma/Transport-routier/Actualites/Pages/Actualites.aspx?IdNews=1',
  'ferroviaire_fr' => 'https://www.transport.gov.ma/ferroviaire/Actualites/Pages/Actualites.aspx?IdNews=1',
  'logistique_fr' => 'https://www.transport.gov.ma/logistique/Actualites/Pages/Actualites.aspx',
  'maritime_fr' => 'https://www.transport.gov.ma/maritime/Actualites/Pages/Actualites.aspx',
  'default_fr' => 'https://www.transport.gov.ma/Actualites/Pages/Actualites.aspx?IdNews=1',
];

function fetchHtml($url) {
  $ch = curl_init();
  curl_setopt_array($ch, [
    CURLOPT_URL => $url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_USERAGENT => 'Drupal Scraper Bot',
  ]);
  
  $html = curl_exec($ch);
  $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
  curl_close($ch);
  
  return ['html' => $html, 'code' => $httpCode];
}

foreach ($urls_to_test as $name => $url) {
  echo "Test de $name:\n";
  echo "URL: $url\n";
  
  $result = fetchHtml($url);
  echo "Code HTTP: " . $result['code'] . "\n";
  
  if ($result['html']) {
    $length = strlen($result['html']);
    echo "Taille HTML: $length caractères\n";
    
    // Chercher des indices de contenu
    if (strpos($result['html'], 'ctl00_SPWebPartManager1_g_1b059c32_8d39_44a2_aea7_47c3d33fd6ad_ctl00_detailPnl') !== false) {
      echo "✓ Div français trouvé\n";
    }
    
    if (strpos($result['html'], 'ctl00_SPWebPartManager1_g_562ad89e_a8c9_4be8_96ce_f25d5c6214f4_ctl00_detailPnl') !== false) {
      echo "✓ Div arabe trouvé\n";
    }
    
    // Chercher des titres h1
    if (preg_match_all('/<h1[^>]*>(.*?)<\/h1>/i', $result['html'], $matches)) {
      echo "Titres H1 trouvés: " . count($matches[1]) . "\n";
      foreach ($matches[1] as $i => $title) {
        $clean_title = strip_tags(trim($title));
        if (!empty($clean_title) && $i < 3) { // Afficher max 3 titres
          echo "  - " . substr($clean_title, 0, 100) . "\n";
        }
      }
    }
  } else {
    echo "✗ Pas de contenu HTML\n";
  }
  
  echo "\n" . str_repeat("-", 50) . "\n\n";
}
