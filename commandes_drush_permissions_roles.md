# 🚀 Commandes Drush - Attribution des Permissions aux Rôles

## 📋 Instructions d'utilisation

Exécutez les commandes suivantes dans l'ordre pour attribuer les permissions aux rôles via Drush.

---

## ✈️ Rôle : `direction_generale_de_l_aviation_civile`

### 🔧 Permissions générales d'accès
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "access content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "access administration pages"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "access content overview"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "view the administration theme"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "access toolbar"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "view published content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "view unpublished content"
```

### ✏️ Permissions générales d'édition de contenu
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit own content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit any content"
```

### 📜 Permissions pour Réglementations
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "create reglementation content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit own reglementation content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit any reglementation content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete own reglementation content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete any reglementation content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "view reglementation revisions"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "revert reglementation revisions"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete reglementation revisions"
```

### 🚧 Permissions pour Projets
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "create projet content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit own projet content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit any projet content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete own projet content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete any projet content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "view projet revisions"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "revert projet revisions"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete projet revisions"
```

### 🤝 Permissions pour Partenaires
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "create partenaire content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit own partenaire content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit any partenaire content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete own partenaire content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete any partenaire content"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "view partenaire revisions"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "revert partenaire revisions"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete partenaire revisions"
```

### 📷 Permissions pour les médias et fichiers
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "create media"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit own media"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit any media"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete own media"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete any media"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "view media"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "upload files"
```

### 🏷️ Permissions pour les taxonomies
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "edit terms in modes_de_transport"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "delete terms in modes_de_transport"
```

### 👁️ Permissions pour les vues
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "access views"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "use views exposed filters"
```

### 📁 Permissions pour les fichiers
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "access files overview"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "view files"
```

### 🔄 Permissions pour les révisions
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "view all revisions"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "revert all revisions"
```

### 📦 Permissions pour les opérations en masse
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "use bulk operations"
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "execute bulk operations"
```

---

## 🚢 Rôle : `direction_de_la_marine_marchande`

### 🔧 Permissions générales d'accès
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "access content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "access administration pages"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "access content overview"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "view the administration theme"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "access toolbar"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "view published content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "view unpublished content"
```

### ✏️ Permissions générales d'édition de contenu
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit own content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit any content"
```

### 📜 Permissions pour Réglementations
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "create reglementation content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit own reglementation content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit any reglementation content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete own reglementation content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete any reglementation content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "view reglementation revisions"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "revert reglementation revisions"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete reglementation revisions"
```

### 🚧 Permissions pour Projets
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "create projet content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit own projet content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit any projet content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete own projet content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete any projet content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "view projet revisions"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "revert projet revisions"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete projet revisions"
```

### 🤝 Permissions pour Partenaires
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "create partenaire content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit own partenaire content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit any partenaire content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete own partenaire content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete any partenaire content"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "view partenaire revisions"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "revert partenaire revisions"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete partenaire revisions"
```

### 📷 Permissions pour les médias et fichiers
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "create media"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit own media"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit any media"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete own media"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete any media"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "view media"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "upload files"
```

### 🏷️ Permissions pour les taxonomies
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "edit terms in modes_de_transport"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "delete terms in modes_de_transport"
```

### 👁️ Permissions pour les vues
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "access views"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "use views exposed filters"
```

### 📁 Permissions pour les fichiers
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "access files overview"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "view files"
```

### 🔄 Permissions pour les révisions
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "view all revisions"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "revert all revisions"
```

### 📦 Permissions pour les opérations en masse
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "use bulk operations"
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "execute bulk operations"
```

---

## 🚛 Rôle : `direction_des_transports_routier`

### 🔧 Permissions générales d'accès
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "access content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "access administration pages"
./vendor/bin/drush role:perm:add direction_des_transports_routier "access content overview"
./vendor/bin/drush role:perm:add direction_des_transports_routier "view the administration theme"
./vendor/bin/drush role:perm:add direction_des_transports_routier "access toolbar"
./vendor/bin/drush role:perm:add direction_des_transports_routier "view published content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "view unpublished content"
```

### ✏️ Permissions générales d'édition de contenu
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit own content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit any content"
```

### 📜 Permissions pour Réglementations
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "create reglementation content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit own reglementation content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit any reglementation content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete own reglementation content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete any reglementation content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "view reglementation revisions"
./vendor/bin/drush role:perm:add direction_des_transports_routier "revert reglementation revisions"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete reglementation revisions"
```

### 🚧 Permissions pour Projets
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "create projet content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit own projet content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit any projet content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete own projet content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete any projet content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "view projet revisions"
./vendor/bin/drush role:perm:add direction_des_transports_routier "revert projet revisions"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete projet revisions"
```

### 🤝 Permissions pour Partenaires
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "create partenaire content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit own partenaire content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit any partenaire content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete own partenaire content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete any partenaire content"
./vendor/bin/drush role:perm:add direction_des_transports_routier "view partenaire revisions"
./vendor/bin/drush role:perm:add direction_des_transports_routier "revert partenaire revisions"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete partenaire revisions"
```

### 📷 Permissions pour les médias et fichiers
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "create media"
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit own media"
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit any media"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete own media"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete any media"
./vendor/bin/drush role:perm:add direction_des_transports_routier "view media"
./vendor/bin/drush role:perm:add direction_des_transports_routier "upload files"
```

### 🏷️ Permissions pour les taxonomies
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "edit terms in modes_de_transport"
./vendor/bin/drush role:perm:add direction_des_transports_routier "delete terms in modes_de_transport"
```

### 👁️ Permissions pour les vues
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "access views"
./vendor/bin/drush role:perm:add direction_des_transports_routier "use views exposed filters"
```

### 📁 Permissions pour les fichiers
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "access files overview"
./vendor/bin/drush role:perm:add direction_des_transports_routier "view files"
```

### 🔄 Permissions pour les révisions
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "view all revisions"
./vendor/bin/drush role:perm:add direction_des_transports_routier "revert all revisions"
```

### 📦 Permissions pour les opérations en masse
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "use bulk operations"
./vendor/bin/drush role:perm:add direction_des_transports_routier "execute bulk operations"
```

---

## 🚀 Script d'exécution rapide

### Pour exécuter toutes les commandes d'un seul coup :

#### Aviation Civile - Commande groupée
```bash
./vendor/bin/drush role:perm:add direction_generale_de_l_aviation_civile "access content,access administration pages,access content overview,view the administration theme,access toolbar,view published content,view unpublished content,edit own content,edit any content,create reglementation content,edit own reglementation content,edit any reglementation content,delete own reglementation content,delete any reglementation content,view reglementation revisions,revert reglementation revisions,delete reglementation revisions,create projet content,edit own projet content,edit any projet content,delete own projet content,delete any projet content,view projet revisions,revert projet revisions,delete projet revisions,create partenaire content,edit own partenaire content,edit any partenaire content,delete own partenaire content,delete any partenaire content,view partenaire revisions,revert partenaire revisions,delete partenaire revisions,create media,edit own media,edit any media,delete own media,delete any media,view media,upload files,edit terms in modes_de_transport,delete terms in modes_de_transport,access views,use views exposed filters,access files overview,view files,view all revisions,revert all revisions,use bulk operations,execute bulk operations"
```

#### Marine Marchande - Commande groupée
```bash
./vendor/bin/drush role:perm:add direction_de_la_marine_marchande "access content,access administration pages,access content overview,view the administration theme,access toolbar,view published content,view unpublished content,edit own content,edit any content,create reglementation content,edit own reglementation content,edit any reglementation content,delete own reglementation content,delete any reglementation content,view reglementation revisions,revert reglementation revisions,delete reglementation revisions,create projet content,edit own projet content,edit any projet content,delete own projet content,delete any projet content,view projet revisions,revert projet revisions,delete projet revisions,create partenaire content,edit own partenaire content,edit any partenaire content,delete own partenaire content,delete any partenaire content,view partenaire revisions,revert partenaire revisions,delete partenaire revisions,create media,edit own media,edit any media,delete own media,delete any media,view media,upload files,edit terms in modes_de_transport,delete terms in modes_de_transport,access views,use views exposed filters,access files overview,view files,view all revisions,revert all revisions,use bulk operations,execute bulk operations"
```

#### Transports Routiers - Commande groupée
```bash
./vendor/bin/drush role:perm:add direction_des_transports_routier "access content,access administration pages,access content overview,view the administration theme,access toolbar,view published content,view unpublished content,edit own content,edit any content,create reglementation content,edit own reglementation content,edit any reglementation content,delete own reglementation content,delete any reglementation content,view reglementation revisions,revert reglementation revisions,delete reglementation revisions,create projet content,edit own projet content,edit any projet content,delete own projet content,delete any projet content,view projet revisions,revert projet revisions,delete projet revisions,create partenaire content,edit own partenaire content,edit any partenaire content,delete own partenaire content,delete any partenaire content,view partenaire revisions,revert partenaire revisions,delete partenaire revisions,create media,edit own media,edit any media,delete own media,delete any media,view media,upload files,edit terms in modes_de_transport,delete terms in modes_de_transport,access views,use views exposed filters,access files overview,view files,view all revisions,revert all revisions,use bulk operations,execute bulk operations"
```

---

## 🔧 Commandes de maintenance

### Vider le cache après modification
```bash
./vendor/bin/drush cache:rebuild
```

### Vérifier les permissions d'un rôle
```bash
./vendor/bin/drush role:list --show-permissions | grep -A 50 "direction_generale_de_l_aviation_civile"
./vendor/bin/drush role:list --show-permissions | grep -A 50 "direction_de_la_marine_marchande"
```

### Exporter la configuration
```bash
./vendor/bin/drush config:export -y
```

---

## 📋 Checklist post-exécution

- [ ] Toutes les commandes ont été exécutées sans erreur
- [ ] Le cache a été vidé avec `drush cache:rebuild`
- [ ] Les permissions ont été vérifiées avec `drush role:list --show-permissions`
- [ ] La configuration a été exportée avec `drush config:export -y`
- [ ] Les utilisateurs concernés ont été informés des nouvelles permissions

---

## 🎯 Résumé des nouvelles permissions ajoutées

✅ **Permissions générales d'édition de contenu** :
- `edit own content` - Permet d'éditer son propre contenu
- `edit any content` - Permet d'éditer n'importe quel contenu

Ces permissions s'ajoutent aux permissions déjà existantes pour une gestion complète du contenu par les deux rôles. 