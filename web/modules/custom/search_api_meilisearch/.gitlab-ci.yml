include:
  # This include centralizes our CI "golden path" https://docs.gitlab.com/ee/ci/yaml/#includefile
  - project: $_GITLAB_TEMPLATES_REPO
    ref: $_GITLAB_TEMPLATES_REF
    file:
      - '/includes/include.drupalci.main.yml'
      - '/includes/include.drupalci.variables.yml'
      - '/includes/include.drupalci.workflows.yml'

#
# Start custom overrides.
#

variables:
  #  Disable default phpunit job in favor of the d9/10 variants below.
  SKIP_PHPUNIT: 1
  SKIP_STYLELINT: 1
  SKIP_ESLINT: 1
  _PHPUNIT_EXTRA: --verbose
  _PHPUNIT_CONCURRENT: 1

# Run two variations of composer job.
composer:
  parallel:
    matrix:
      - _TARGET_PHP: [ "7.4" ]
        _TARGET_CORE: [ "9.5.10" ]
      - _TARGET_PHP: [ "8.1" ]
        _TARGET_CORE: [ "$CORE_STABLE" ]

#
# The 4 validation jobs below are explicit about their artifact. This is not strictly needed
# (last wins) but done for demonstration. https://docs.gitlab.com/ee/ci/jobs/job_control.html#fetch-artifacts-from-a-parallelmatrix-job
#

phpcs:
  # Set allow_failure to false so that the test fails for coding standards faults.
  allow_failure: false
  needs:
    - "composer: [8.1, $CORE_STABLE]"

composer-lint:
  needs:
    - "composer: [8.1, $CORE_STABLE]"

eslint:
  needs:
    - "composer: [8.1, $CORE_STABLE]"

stylelint:
  needs:
    - "composer: [8.1, $CORE_STABLE]"

.phpunit-local:
  services:
    - !reference [ .with-database ]
    - !reference [ .with-chrome ]
    - name: getmeili/meilisearch:v1.3.3
      alias: meilisearch
      variables:
        MEILI_MASTER_KEY: MASTER_KEY
  variables:
    SKIP_PHPUNIT: 0
# An example of further matrix splitting of jobs.
#  parallel:
#    matrix:
#      # Run test groups in parallel for better dev velocity.
#      - _PHPUNIT_EXTRA: [ "--group devel", "--group devel_generate" ]

phpunit-d9:
  needs:
    - job: composer
      parallel:
        matrix:
          - _TARGET_PHP: "7.4"
            _TARGET_CORE: "9.5.10"
  variables:
    _TARGET_PHP: "7.4"
  extends:
    - .phpunit-base
    - .phpunit-local

phpunit-d10:
  needs:
    - job: composer
      parallel:
        matrix:
          - _TARGET_PHP: "8.1"
            _TARGET_CORE: $CORE_STABLE
  variables:
    _TARGET_PHP: "8.1"
  extends:
    - .phpunit-base
    - .phpunit-local
