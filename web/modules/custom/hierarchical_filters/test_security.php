<?php

/**
 * Script de test pour vérifier la sécurité du module Hierarchical Filters
 */

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

// Bootstrap Drupal
$autoloader = require_once 'autoload.php';
$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

// Test de sécurité : vérifier qu'on ne peut pas accéder aux domaines sans secteur valide
echo "=== Test de sécurité du module Hierarchical Filters ===\n\n";

// Test 1: Secteur inexistant
echo "Test 1: Secteur inexistant (ID: 99999)\n";
$domains = hierarchical_filters_get_domains_for_secteur(99999);
echo "Résultat: " . count($domains) . " domaines trouvés\n";
if (empty($domains)) {
    echo "✅ SÉCURITÉ OK: Aucun domaine retourné pour un secteur inexistant\n";
} else {
    echo "❌ PROBLÈME DE SÉCURITÉ: Des domaines ont été retournés!\n";
    print_r($domains);
}
echo "\n";

// Test 2: Secteur valide
echo "Test 2: Test avec un secteur valide\n";
$query = \Drupal::entityQuery('taxonomy_term')
    ->accessCheck(TRUE)
    ->condition('vid', 'secteurs')
    ->range(0, 1);
$secteur_ids = $query->execute();

if (!empty($secteur_ids)) {
    $secteur_id = reset($secteur_ids);
    echo "Test avec secteur ID: $secteur_id\n";
    $domains = hierarchical_filters_get_domains_for_secteur($secteur_id);
    echo "Résultat: " . count($domains) . " domaines trouvés\n";
    if (!empty($domains)) {
        echo "✅ FONCTIONNEMENT OK: Des domaines ont été retournés pour un secteur valide\n";
        echo "Premiers domaines:\n";
        $count = 0;
        foreach ($domains as $id => $name) {
            echo "  - $id: $name\n";
            if (++$count >= 3) break;
        }
    } else {
        echo "⚠️  ATTENTION: Aucun domaine trouvé pour ce secteur (normal si pas de données)\n";
    }
} else {
    echo "⚠️  ATTENTION: Aucun secteur trouvé dans la taxonomie\n";
}
echo "\n";

// Test 3: Vérifier la structure des données
echo "Test 3: Vérification de la structure des données\n";
$secteurs_count = \Drupal::entityQuery('taxonomy_term')
    ->accessCheck(TRUE)
    ->condition('vid', 'secteurs')
    ->count()
    ->execute();

$domaines_count = \Drupal::entityQuery('taxonomy_term')
    ->accessCheck(TRUE)
    ->condition('vid', 'domaines_d_activites')
    ->count()
    ->execute();

echo "Nombre de secteurs: $secteurs_count\n";
echo "Nombre de domaines: $domaines_count\n";

if ($secteurs_count > 0 && $domaines_count > 0) {
    echo "✅ STRUCTURE OK: Les taxonomies contiennent des données\n";
} else {
    echo "⚠️  ATTENTION: Taxonomies vides - vérifiez vos données\n";
}

echo "\n=== Fin des tests ===\n"; 