(function ($, Drupal, once) {
  'use strict';

  Drupal.behaviors.hierarchicalFiltersBehavior = {
    attach: function (context, settings) {
      
      // Use Drupal's once() function on the form itself
      once('hierarchicalFiltersBehavior', 'form[id*="views-exposed-form-reglementation"], form[id*="views-exposed-form-procedure-formulaire"]', context).forEach(function (form) {
        var $form = $(form);
        
        // Try to find secteur field (can be select or radio buttons)
        var secteurSelect = $form.find('select[name="field_secteur_target_id"]');
        var secteurRadios = $form.find('input[name="field_secteur_target_id"]');
        var domaineCheckboxes = $form.find('input[name^="field_domaine_d_activite_target_id"]');
        
        // Determine which secteur field type we're working with
        var usingSecteurSelect = secteurSelect.length > 0;
        var usingSecteurRadios = secteurRadios.length > 0;
        
        if (!usingSecteurSelect && !usingSecteurRadios) {
          return;
        }
        
        if (domaineCheckboxes.length === 0) {
          return;
        }
        
        // Initial setup - hide domaine container if no secteur is selected
        var initialSecteurValue = getCurrentSecteurValue();
        if (!initialSecteurValue || initialSecteurValue === 'All') {
          hideDomaineContainer();
        } else {
          loadDomainesBySecteur(initialSecteurValue);
        }
        
        // Event listeners for secteur changes
        if (usingSecteurSelect) {
          secteurSelect.on('change', handleSecteurChange);
        }
        
        if (usingSecteurRadios) {
          secteurRadios.on('change', handleSecteurChange);
        }
        
        // Function to get current secteur value
        function getCurrentSecteurValue() {
          if (usingSecteurSelect) {
            return secteurSelect.val();
          } else if (usingSecteurRadios) {
            return secteurRadios.filter(':checked').val();
          }
          return null;
        }
        
        // Function to handle secteur change
        function handleSecteurChange() {
          var secteurId = getCurrentSecteurValue();
          
          if (!secteurId || secteurId === 'All') {
            hideDomaineContainer();
          } else {
            loadDomainesBySecteur(secteurId);
          }
        }
        
        // Function to hide domaine container completely
        function hideDomaineContainer() {
          domaineCheckboxes.prop('disabled', true);
          domaineCheckboxes.prop('checked', false);
          
          // Hide the entire domaine container including label
          var domaineContainer = domaineCheckboxes.closest('.form-item, .form-checkboxes');
          if (domaineContainer.length) {
            domaineContainer.hide();
            
            // Also hide the fieldset or wrapper that contains the label
            var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
            if (domaineFieldset.length) {
              domaineFieldset.hide();
            }
            
            // Remove any existing messages
            var messageId = 'hierarchical-filters-message';
            var existingMessage = domaineContainer.find('#' + messageId);
            if (existingMessage.length) {
              existingMessage.remove();
            }
          }
        }

        // Function to load domaines by secteur
        function loadDomainesBySecteur(secteurId) {
          
          // Save current domaine selections
          var selectedDomaines = [];
          domaineCheckboxes.filter(':checked').each(function() {
            var match = this.name.match(/\[(\d+)\]/);
            if (match) {
              selectedDomaines.push(match[1]);
            }
          });
          
          // Hide all checkboxes immediately for better UX (no flash)
          domaineCheckboxes.each(function() {
            $(this).closest('label, .form-item').hide();
          });
          
          // Disable all checkboxes during loading
          domaineCheckboxes.prop('disabled', true);
          
          // Show and prepare domaine container (now with hidden content)
          var domaineContainer = domaineCheckboxes.closest('.form-item, .form-checkboxes');
          if (domaineContainer.length) {
            // Don't call show() on container as it makes all children visible again
            domaineContainer.addClass('hierarchical-filters-loading');
            
            // Also show the fieldset or wrapper that contains the label
            var domaineFieldset = domaineContainer.closest('fieldset, .fieldgroup');
            if (domaineFieldset.length) {
              domaineFieldset.show();
            }
            
            // Remove any existing messages
            var messageId = 'hierarchical-filters-message';
            var existingMessage = domaineContainer.find('#' + messageId);
            if (existingMessage.length) {
              existingMessage.remove();
            }
          }

          $.ajax({
            url: Drupal.url('domaines-by-secteur/' + secteurId),
            type: 'GET',
            dataType: 'json',
            timeout: 10000,
            success: function (response) {
              
              // Remove loading state
              if (domaineContainer.length) {
                domaineContainer.removeClass('hierarchical-filters-loading hierarchical-filters-disabled');
              }
              
              var availableDomaines = [];
              
              if (response && response.length > 0) {
                // Show only relevant checkboxes
                response.forEach(function(domaine) {
                  availableDomaines.push(domaine.id);
                  var checkbox = domaineCheckboxes.filter('[name*="[' + domaine.id + ']"]');
                  if (checkbox.length) {
                    checkbox.prop('disabled', false);
                    checkbox.closest('label, .form-item').show();
                  }
                });
              } else {
                // No domains for this sector - keep all checkboxes hidden
                // All checkboxes remain disabled and hidden
              }
              
              // Restore previous selections if they are still valid
              selectedDomaines.forEach(function(domaineId) {
                if (availableDomaines.includes(domaineId)) {
                  var checkbox = domaineCheckboxes.filter('[name*="[' + domaineId + ']"]');
                  if (checkbox.length) {
                    checkbox.prop('checked', true);
                  }
                }
              });
            },
            error: function (xhr, status, error) {
              
              // Remove loading state
              if (domaineContainer.length) {
                domaineContainer.removeClass('hierarchical-filters-loading');
              }
              
              // On error, keep all checkboxes hidden and disabled
              // Don't show all checkboxes as this could be a security issue
            }
          });
        }
      });
    }
  };
})(jQuery, Drupal, once);