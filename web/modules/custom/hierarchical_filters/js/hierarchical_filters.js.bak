(function ($, Drupal) {
  'use strict';

  Drupal.behaviors.hierarchicalFilters = {
    attach: function (context) {
      console.log('Initialisation des filtres hiérarchiques...');
      
      // Afficher toutes les cases à cocher disponibles pour le débogage
      var allCheckboxes = document.querySelectorAll('input[type="checkbox"]');
      console.log('Toutes les cases à cocher trouvées:', Array.from(allCheckboxes).map(function(checkbox) {
        return {
          name: checkbox.name,
          value: checkbox.value,
          id: checkbox.id,
          checked: checkbox.checked,
          outerHTML: checkbox.outerHTML
        };
      }));
      
      // Essayer de trouver les cases à cocher pour le secteur
      var $secteurCheckboxes = $('input[type="checkbox"][name^="field_secteur_target_id"]', context);
      
      if ($secteurCheckboxes.length === 0) {
        console.log('Aucune case à cocher de secteur trouvée. Liste des cases à cocher disponibles:');
        $('input[type="checkbox"]', context).each(function() {
          console.log('- name:', $(this).attr('name'), '| value:', $(this).val(), '| checked:', $(this).is(':checked'));
        });
        return;
      }
      
      // Si on arrive ici, on a trouvé des cases à cocher de secteur
      console.log('Cases à cocher de secteur trouvées:', $secteurCheckboxes.length);
      
      // Trouver le conteneur parent commun pour les cases à cocher
      var $secteurContainer = $secteurCheckboxes.first().closest('.form-item, .form-type-checkbox, .form-checkboxes, .bef-checkboxes, .views-exposed-form');
      if ($secteurContainer.length === 0) {
        console.log('Conteneur des cases à cocher non trouvé, utilisation du body comme fallback');
        $secteurContainer = $('body');
      }
      
      // Afficher la structure HTML pour le débogage
      console.log('Structure HTML autour du conteneur secteur:', $secteurContainer.parent().html());
      
      // Essayer plusieurs sélecteurs pour trouver le conteneur de domaine
      var domaineSelectors = [
        '.form-item-field-domaine-d-activites-target-id',
        '[data-drupal-selector*="field-domaine-d-activites"]',
        '#edit-field-domaine-d-activites-target-id-wrapper',
        '.form-item-field-domaine',
        '.form-item-domaine',
        '.form-item:has(label:contains("domaine"))',
        '.form-item:has(label:contains("Domaine"))',
        '.form-type-select:has(select[name*="domaine"])',
        $secteurContainer.nextAll().filter(':has(select[name*="domaine"]), :has(input[name*="domaine"])').first()
      ];
      
      var $domaineContainer = null;
      
      // Essayer chaque sélecteur jusqu'à trouver un conteneur valide
      for (var i = 0; i < domaineSelectors.length && (!$domaineContainer || $domaineContainer.length === 0); i++) {
        var selector = domaineSelectors[i];
        if (typeof selector === 'string') {
          $domaineContainer = $(selector, context).first();
          if ($domaineContainer.length) {
            console.log('Conteneur de domaine trouvé avec le sélecteur:', selector);
          }
        } else if (selector && selector.length) {
          $domaineContainer = selector;
          console.log('Conteneur de domaine trouvé avec un sélecteur jQuery personnalisé');
        }
      }
      
      // Si aucun conteneur n'a été trouvé, en créer un nouveau
      if (!$domaineContainer || $domaineContainer.length === 0) {
        console.log('Aucun conteneur de domaine trouvé. Création d\'un nouveau conteneur.');
        $domaineContainer = $(
          '<div class="form-item form-type-select form-item-field-domaine-d-activites-target-id">' +
          '  <label for="edit-field-domaine-d-activites-target-id">Domaine d\'activités</label>' +
          '  <div class="form-checkboxes bef-select-as-radios" id="hierarchical-domaine-container"></div>' +
          '</div>'
        );
        $secteurContainer.after($domaineContainer);
      } else {
        console.log('Conteneur de domaine trouvé:', $domaineContainer);
      }
      
      // Trouver ou créer le conteneur pour les boutons radio des domaines
      var $domaineWrapper = $('.form-item-field-domaine-d-activites-target-id, .form-item-field-domaine-d-activite-target-id');
      
      if ($domaineWrapper.length === 0) {
        console.log('Aucun conteneur de domaine trouvé. Création d\'un nouveau conteneur.');
        
        // Essayer de trouver le conteneur avec des sélecteurs plus précis
        $domaineWrapper = $('.form-item-field-domaine-d-activites-target-id-select, .form-item-field-domaine-d-activite-target-id-select');
        
        if ($domaineWrapper.length === 0) {
          // Créer un conteneur pour les boutons radio des domaines
          $domaineWrapper = $('<div>', {
            'class': 'form-item form-type-select form-item-field-domaine-d-activites-target-id hierarchical-filters-domaine-container',
            'id': 'hierarchical-filters-domaine-container'
          });
          
          // Essayer de trouver le conteneur du filtre de domaine avec des sélecteurs alternatifs
          var $domaineFilter = $('.views-exposed-form .form-item-field-domaine-d-activites-target-id, ' +
                               '.views-exposed-form .form-item-field-domaine-d-activite-target-id, ' +
                               '.form-item-field-domaine-d-activites-target-id-select, ' +
                               '.form-item-field-domaine-d-activite-target-id-select');
          
          if ($domaineFilter.length > 0) {
            console.log('Conteneur de filtre de domaine trouvé, remplacement...');
            $domaineFilter.replaceWith($domaineWrapper);
          } else {
            // Si on ne trouve pas le conteneur, on l'ajoute après le conteneur des secteurs
            console.log('Ajout du conteneur après le conteneur des secteurs');
            $secteurContainer.after($domaineWrapper);
          }
        } else {
          console.log('Conteneur de domaine trouvé avec un sélecteur alternatif:', $domaineWrapper.attr('class'));
        }
      } else {
        console.log('Wrapper de domaine trouvé:', $domaineWrapper.attr('class'));
      }
      
      // Vérifier si le conteneur est visible
      console.log('Visibilité du conteneur de domaine:', $domaineWrapper.is(':visible'));
      console.log('Dimensions du conteneur de domaine:', {
        width: $domaineWrapper.width(),
        height: $domaineWrapper.height(),
        outerWidth: $domaineWrapper.outerWidth(),
        outerHeight: $domaineWrapper.outerHeight()
      });
      
      // Vider le conteneur des domaines
      $domaineWrapper.empty();
      
      // Créer un conteneur pour les boutons radio
      var $radioContainer = $('<div>', { 
        'class': 'form-radios',
        'id': 'hierarchical-filters-domaine-radios'
      });
      
      $domaineWrapper.append($radioContainer);
      
      console.log('Conteneur de domaines créé avec succès');

      // Fonction pour charger les domaines d'un secteur
      function loadDomains(secteurId) {
        if (!secteurId) {
          console.log('Aucun secteur sélectionné');
          return;
        }
        
        console.log('Début du chargement des domaines pour secteur:', secteurId);
        
        // Afficher un indicateur de chargement
        $domaineWrapper.html('<div class="ajax-progress ajax-progress-throbber"><div class="throbber">&nbsp;</div> Chargement des domaines...</div>');
        
        // Faire une requête AJAX pour récupérer les domaines du secteur
        var ajaxUrl = '/hierarchical-filters/get-domains/' + secteurId;
        console.log('Envoi de la requête AJAX vers', ajaxUrl);
        
        // Fonction pour afficher une erreur
        function showError(message, details) {
          console.error('Erreur:', message, details || '');
          $domaineWrapper.html(
            '<div class="messages messages--error">' +
            '  <h2>Erreur</h2>' +
            '  <p>' + message + '</p>' +
            (details ? '<p class="error-details">' + details + '</p>' : '') +
            '</div>'
          );
        }
        
        // Effectuer la requête AJAX
        $.ajax({
          url: ajaxUrl,
          method: 'GET',
          dataType: 'json',
          beforeSend: function() {
            console.log('Requête AJAX en cours...');
          },
          error: function(xhr, status, error) {
            showError(
              'Impossible de charger les domaines. Veuillez réessayer plus tard.',
              'Erreur: ' + error + ' (Status: ' + status + ')'
            );
          },
          complete: function() {
            console.log('Requête AJAX terminée');
          },
          success: function(response, textStatus, jqXHR) {
            try {
              console.log('=== DÉBUT RÉPONSE AJAX ===');
              console.log('URL:', jqXHR.responseURL);
              console.log('Statut:', jqXHR.status, jqXHR.statusText);
              console.log('En-têtes:', jqXHR.getAllResponseHeaders());
              console.log('Réponse brute:', jqXHR.responseText);
              console.log('Réponse parsée:', response);
              console.log('=== FIN RÉPONSE AJAX ===');
              
              // Vérifier la réponse
              if (!response) {
                throw new Error('Aucune réponse du serveur');
              }
              
              if (response.status !== 'success') {
                throw new Error('Le serveur a renvoyé une erreur: ' + (response.message || 'Raison inconnue'));
              }
              
              if (!response.domains || !Array.isArray(response.domains)) {
                throw new Error('Format de réponse inattendu du serveur: ' + JSON.stringify(response));
              }
              
              console.log('Domaines reçus:', response.domains);
              console.log('Nombre de domaines:', response.domains.length);
              
              // Vérifier s'il y a des domaines
              if (response.domains.length === 0) {
                console.warn('Aucun domaine disponible pour ce secteur');
                showError('Aucun domaine disponible', 'Aucun domaine trouvé pour le secteur sélectionné.');
                return;
              }
              
              // Créer un conteneur pour les boutons radio
              var $radiosContainer = $('<div>', { 
                'class': 'form-radios',
                'id': 'hierarchical-filters-domaine-radios'
              });
              
              console.log('Création du conteneur de boutons radio:', $radiosContainer[0]);
              
              // Créer les boutons radio pour chaque domaine
              response.domains.forEach(function(domain, index) {
                console.log('Traitement du domaine #' + index + ':', domain);
                if (!domain.id || !domain.label) {
                  console.warn('Domaine invalide:', domain);
                  return;
                }
                
                var radioId = 'edit-field-domaine-d-activites-target-id-' + domain.id;
                var $wrapper = $('<div>', { 
                  'class': 'js-form-item form-item js-form-type-radio form-item-field-domaine-d-activites-target-id' 
                });
                
                var $radio = $('<input>', {
                  type: 'radio',
                  id: radioId,
                  name: 'field_domaine_d_activites_target_id',
                  value: domain.id,
                  'data-drupal-selector': radioId,
                  'class': 'form-radio'
                });
                
                var $label = $('<label>', {
                  'for': radioId,
                  'class': 'option',
                  'data-drupal-selector': 'edit-field-domaine-d-activites-target-id-' + domain.id + '-label',
                  text: domain.label
                });
                
                $wrapper.append($radio, $label);
                $radiosContainer.append($wrapper);
                console.log('Bouton radio créé pour le domaine:', domain.label, 'ID:', domain.id, 'Élément:', $wrapper[0]);
              });
              
              // Vider le conteneur et ajouter les boutons radio
              console.log('Contenu du conteneur avant vidage:', $domaineWrapper.html());
              $domaineWrapper.empty();
              console.log('Conteneur vidé. Ajout du conteneur de boutons radio...');
              $domaineWrapper.append($radiosContainer);
              console.log('Boutons radio ajoutés. Contenu final:', $domaineWrapper.html());
              
              // Vérifier si les boutons sont dans le DOM
              setTimeout(function() {
                console.log('Vérification du DOM après ajout:');
                  
                  // Mettre à jour l'URL sans recharger la page
                  history.pushState({}, '', url.pathname + '?' + params.toString());
                  
                  // Déclencher le rechargement de la vue
                  if (Drupal.views && Drupal.views.instances && Drupal.views.instances[0]) {
                    Drupal.views.instances[0].view.triggerRefresh();
                  }
                });
              
            } catch (error) {
              console.error('Erreur lors du traitement de la réponse:', error);
              showError('Erreur lors du chargement des domaines', error.message);
      // Charger les domaines si un secteur est déjà sélectionné
      var $selectedSecteur = $secteurCheckboxes.filter(':checked').first();
      if ($selectedSecteur.length > 0) {
        loadDomains($selectedSecteur.val());
      }
    }
  };

})(jQuery, Drupal);