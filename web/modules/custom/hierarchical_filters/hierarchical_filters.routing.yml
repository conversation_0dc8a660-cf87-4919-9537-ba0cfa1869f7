hierarchical_filters.get_domains:
  path: '/api/hierarchical-filters/domains/{secteur_id}'
  defaults:
    _controller: '\Drupal\hierarchical_filters\Controller\HierarchicalFiltersController::getDomains'
    _format: 'json'
  methods: [GET]
  requirements:
    _permission: 'access content'
    secteur_id: '\d+'
    _format: 'json'
  options:
    _admin_route: FALSE
    no_cache: TRUE
    parameters:
      secteur_id:
        type: 'integer'

# Route alternative plus simple pour tester
hierarchical_filters.get_domains_simple:
  path: '/hierarchical-filters-domains/{secteur_id}'
  defaults:
    _controller: '\Drupal\hierarchical_filters\Controller\HierarchicalFiltersController::getDomains'
  methods: [GET]
  requirements:
    _permission: 'access content'
    secteur_id: '\d+'
  options:
    no_cache: TRUE

hierarchical_filters.domaines_by_secteur:
  path: '/domaines-by-secteur/{secteur_id}'
  defaults:
    _controller: '\Drupal\hierarchical_filters\Controller\HierarchicalFiltersController::getDomainesBySecteur'
  requirements:
    _access: 'TRUE'
    secteur_id: \d+
  methods: [GET]
