# Module Hierarchical Filters

## Description

Ce module ajoute des **filtres hiérarchiques** à la vue `reglementation`. Il permet aux utilisateurs de :

1. Sélectionner d'abord un **secteur** via des boutons radio ou une liste déroulante
2. Voir apparaître automatiquement un **deuxième filtre** avec les **domaines d'activités** liés à ce secteur
3. Filtrer les résultats en fonction des deux critères sélectionnés

## Fonctionnalités

- **Filtre en cascade** : Le filtre "Domaine d'activité" se met à jour dynamiquement selon le secteur sélectionné
- **Chargement AJAX** : Les domaines d'activités sont chargés dynamiquement via AJAX
- **Interface épurée** : Aucun message textuel n'encombre l'interface utilisateur
- **Masquage intelligent** : Le filtre domaine d'activité (libellé inclus) est complètement masqué tant qu'aucun secteur n'est sélectionné
- **Performance optimisée** : Seuls les domaines liés au secteur sélectionné sont chargés et affichés
- **Gestion d'URL** : Les filtres sont reflétés dans l'URL pour permettre le partage et la navigation
- **Préservation des sélections** : Les choix précédents sont conservés lors du changement de secteur (si compatibles)

## Architecture

### Fichiers principaux

- `hierarchical_filters.module` : Hooks Drupal et logique principale
- `src/Controller/HierarchicalFiltersController.php` : Contrôleur AJAX pour récupérer les domaines
- `js/hierarchical_filters.js` : Logique JavaScript pour la gestion des interactions
- `css/hierarchical_filters.css` : Styles CSS pour les états de chargement
- `hierarchical_filters.routing.yml` : Définition des routes AJAX
- `hierarchical_filters.libraries.yml` : Déclaration des assets
- `hierarchical_filters.info.yml` : Informations du module

### Fonctionnement

1. **Détection des champs** : Le module détecte automatiquement les champs de secteur dans le formulaire exposé (select ou radio)
2. **Masquage initial** : Si aucun secteur n'est sélectionné, le filtre domaine d'activité est complètement masqué
3. **Écoute des changements** : Un écouteur surveille les changements sur les champs secteur
4. **Chargement AJAX** : Lorsqu'un secteur est sélectionné, une requête AJAX récupère les domaines associés
5. **Affichage dynamique** : Le filtre domaine d'activité apparaît avec uniquement les options pertinentes
6. **Filtrage** : La sélection d'un domaine met à jour l'URL et recharge la page avec les filtres appliqués

### Structure des données

Le module s'appuie sur :
- **Vocabulaire taxonomique "secteurs"** : Contient les secteurs d'activité
- **Vocabulaire taxonomique "domaines_d_activites"** : Contient les domaines avec référence aux secteurs via `field_secteur`
- **Vue "reglementation"** : Vue configurée avec les filtres exposés

## Installation

1. Placer le module dans `web/modules/custom/hierarchical_filters/`
2. Activer le module : `vendor/bin/drush en hierarchical_filters -y`
3. Vider le cache : `vendor/bin/drush cr`

## Configuration requise

### Taxonomies

1. **Vocabulaire "secteurs"** avec les termes de secteurs
2. **Vocabulaire "domaines_d_activites"** avec :
   - Les termes de domaines d'activité
   - Un champ `field_secteur` qui référence les termes du vocabulaire "secteurs"

### Vue

1. **Vue "reglementation"** avec :
   - Display "page_1"
   - Filtre exposé `field_secteur_target_id` (select ou radio)
   - Filtre exposé `field_domaine_d_activite_target_id` (checkboxes - géré dynamiquement)

## Utilisation

1. Accéder à la vue configurée avec les filtres exposés
2. **État initial** : Seul le filtre secteur est visible
3. **Sélection secteur** : Le filtre domaine d'activité apparaît automatiquement avec les options correspondantes
4. **Sélection domaine** : Choisir un ou plusieurs domaines pour affiner les résultats
5. **Changement secteur** : Les sélections de domaines compatibles sont préservées

## Interface utilisateur

### Comportement visuel

- **Aucun secteur sélectionné** : 
  - Seul le filtre secteur est visible
  - Le filtre domaine d'activité est complètement masqué (libellé inclus)
  
- **Secteur sélectionné** :
  - Le filtre domaine d'activité apparaît avec son libellé
  - Seules les options pertinentes sont affichées
  - État de chargement avec animation CSS pendant la requête AJAX

- **Interface épurée** :
  - Aucun message textuel informatif
  - Pas de texte "Sélectionnez d'abord un secteur..."
  - Pas de compteur "X domaines disponibles..."
  - Messages d'erreur uniquement dans la console (pour le débogage)

## Routes AJAX

Le module expose plusieurs routes pour tester et utiliser l'endpoint :

- `/domaines-by-secteur/{secteur_id}` : Route principale utilisée par le JavaScript
- `/api/hierarchical-filters/domains/{secteur_id}` : Route API alternative avec format JSON strict
- `/hierarchical-filters-domains/{secteur_id}` : Route simple pour les tests

## Corrections et améliorations apportées

### Version actuelle

1. **Interface épurée** : Suppression de tous les messages textuels sur l'interface
2. **Masquage intelligent** : Le filtre domaine d'activité et son libellé sont complètement masqués quand non pertinents
3. **Gestion robuste des conteneurs** : Détection et masquage des fieldsets et wrappers parents
4. **Performance optimisée** : Requêtes AJAX efficaces avec gestion d'erreurs en console uniquement

### Corrections précédentes

1. **Duplication de timestamp** dans `drupalSettings` corrigée
2. **Incohérences dans les noms de champs** entre le module et le contrôleur résolues
3. **Logique AJAX améliorée** avec gestion d'erreurs appropriée
4. **Fonction de récupération des domaines optimisée** pour interroger directement la taxonomie
5. **Code JavaScript simplifié** et dédupliqué
6. **Gestion des URL améliorée** pour le partage et la navigation

## Dépannage

### Problèmes courants

1. **Le filtre domaine d'activité ne s'affiche pas** :
   - Vérifiez la console du navigateur pour les erreurs JavaScript
   - Assurez-vous que les termes de taxonomie sont correctement liés entre secteurs et domaines
   - Vérifiez que le champ `field_secteur` existe dans le vocabulaire "domaines_d_activites"

2. **Erreurs AJAX** :
   - Consultez la console du navigateur (F12) pour les messages d'erreur détaillés
   - Vérifiez les logs Drupal : `vendor/bin/drush watchdog:show --filter=hierarchical_filters`
   - Assurez-vous que l'utilisateur a les permissions `access content`
   - Testez la route AJAX directement : `/domaines-by-secteur/{secteur_id}`

3. **Le filtre ne se masque pas correctement** :
   - Vérifiez que les sélecteurs CSS correspondent à votre structure HTML
   - Inspectez les éléments DOM pour identifier les bons conteneurs parents

### Débogage

Pour activer le mode débogage :
1. Ouvrez la console du navigateur (F12)
2. Les messages de débogage JavaScript sont automatiquement affichés
3. Consultez les logs Drupal pour les messages du module côté serveur
4. Utilisez les outils de développement pour inspecter les requêtes AJAX

### Test de l'endpoint

Un script de test est disponible dans `test_endpoint.php` :
```bash
vendor/bin/drush php:script web/modules/custom/hierarchical_filters/test_endpoint.php
```

## Personnalisation

### Adapter à d'autres vues

Pour utiliser le module avec d'autres vues :
1. Modifier le sélecteur de formulaire dans le JavaScript : `'form[id*="views-exposed-form-VOTRE_VUE"]'`
2. Ajuster les noms de champs selon votre configuration
3. Vérifier que les vocabulaires taxonomiques correspondent

### Styles CSS

Les styles par défaut se trouvent dans `css/hierarchical_filters.css` :
- États de chargement avec animations
- Styles pour les états désactivés
- Classes utilitaires pour le masquage

Vous pouvez surcharger ces styles dans votre thème si nécessaire.

## Dépendances

- Drupal Core Views
- Drupal Core Taxonomy  
- Drupal Core Node
- jQuery (inclus dans Drupal Core)
- Drupal Core Once (pour éviter les initialisations multiples)

## Compatibilité

- Drupal 10.x
- Drupal 11.x
- Compatible avec les thèmes personnalisés et les distributions Drupal

## Support

Pour toute question ou problème :
1. Consulter les logs Drupal : Channel `hierarchical_filters`
2. Vérifier la console du navigateur pour les erreurs JavaScript
3. S'assurer que la configuration des taxonomies est correcte
4. Tester l'endpoint AJAX directement via l'URL
5. Utiliser le script de test fourni pour valider le fonctionnement 