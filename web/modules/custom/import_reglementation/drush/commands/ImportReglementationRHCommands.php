<?php

namespace Drush\Commands\import_reglementation;

use Drush\Commands\DrushCommands;
use Drupal\import_reglementation\Service\CsvImporterRH;

/**
 * Commandes Drush pour l'importation de réglementations RH et textes généraux.
 */
class ImportReglementationRHCommands extends DrushCommands {

  /**
   * Le service d'importation CSV RH.
   *
   * @var \Drupal\import_reglementation\Service\CsvImporterRH
   */
  protected $csvImporterRH;

  /**
   * Constructeur.
   *
   * @param \Drupal\import_reglementation\Service\CsvImporterRH $csv_importer_rh
   *   Le service d'importation CSV RH.
   */
  public function __construct(CsvImporterRH $csv_importer_rh) {
    $this->csvImporterRH = $csv_importer_rh;
  }

  /**
   * Importe les réglementations RH depuis un fichier CSV.
   *
   * @param string $file_path
   *   Chemin vers le fichier CSV.
   * @param array $options
   *   Options de la commande.
   *
   * @command import-reglementation:csv-rh
   * @aliases irh
   * @option delimiter Le délimiteur CSV (par défaut: ,)
   * @usage import-reglementation:csv-rh /path/to/file.csv
   *   Importe les réglementations RH depuis le fichier CSV spécifié.
   */
  public function importCsvRH($file_path, array $options = ['delimiter' => ',']) {
    $delimiter = $options['delimiter'];
    
    $this->output()->writeln("Début de l'importation RH depuis: $file_path");
    $this->output()->writeln("Délimiteur: $delimiter");
    
    // Vérifier que le fichier existe
    if (!file_exists($file_path)) {
      $this->logger()->error("Le fichier $file_path n'existe pas.");
      return;
    }
    
    // Effectuer l'importation
    $results = $this->csvImporterRH->import($file_path, $delimiter);
    
    // Afficher les résultats
    $this->output()->writeln("Résultats de l'importation RH:");
    $this->output()->writeln("- Succès: " . ($results['success'] ? 'Oui' : 'Non'));
    $this->output()->writeln("- Créés: " . $results['created']);
    $this->output()->writeln("- Mis à jour: " . $results['updated']);
    $this->output()->writeln("- Traités: " . $results['processed']);
    $this->output()->writeln("- Erreurs: " . count($results['errors']));
    
    if (!empty($results['errors'])) {
      $this->output()->writeln("\nErreurs détaillées:");
      foreach ($results['errors'] as $error) {
        $this->logger()->error($error);
      }
    }
    
    if ($results['success']) {
      $this->logger()->success("Importation RH terminée avec succès!");
    } else {
      $this->logger()->error("L'importation RH a échoué.");
    }
  }

  /**
   * Valide un fichier CSV RH avant importation.
   *
   * @param string $file_path
   *   Chemin vers le fichier CSV.
   * @param array $options
   *   Options de la commande.
   *
   * @command import-reglementation:validate-rh
   * @aliases vrh
   * @option delimiter Le délimiteur CSV (par défaut: ,)
   * @usage import-reglementation:validate-rh /path/to/file.csv
   *   Valide la structure du fichier CSV RH.
   */
  public function validateCsvRH($file_path, array $options = ['delimiter' => ',']) {
    $delimiter = $options['delimiter'];
    
    $this->output()->writeln("Validation du fichier CSV RH: $file_path");
    
    if (!file_exists($file_path)) {
      $this->logger()->error("Le fichier $file_path n'existe pas.");
      return;
    }

    if (($handle = fopen($file_path, 'r')) !== FALSE) {
      // Lire l'en-tête
      $header = fgetcsv($handle, 0, $delimiter);
      
      if (!$header) {
        $this->logger()->error("Impossible de lire l'en-tête du fichier.");
        fclose($handle);
        return;
      }

      $this->output()->writeln("Colonnes détectées:");
      foreach ($header as $index => $column) {
        $this->output()->writeln("  $index: $column");
      }

      // Vérifier les colonnes requises
      $required_columns = [
        'Sous secteur', 'Type', 'Thème', 'N° du texte', 
        'Intitulé en Français', 'Date De publication'
      ];
      
      $missing_columns = [];
      foreach ($required_columns as $column) {
        if (!in_array($column, $header)) {
          $missing_columns[] = $column;
        }
      }

      if (empty($missing_columns)) {
        $this->logger()->success("Toutes les colonnes requises sont présentes.");
      } else {
        $this->logger()->error("Colonnes manquantes: " . implode(', ', $missing_columns));
      }

      // Compter les lignes
      $line_count = 0;
      while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
        $line_count++;
      }

      $this->output()->writeln("Nombre de lignes de données: $line_count");
      fclose($handle);

    } else {
      $this->logger()->error("Impossible d'ouvrir le fichier CSV.");
    }
  }
}
