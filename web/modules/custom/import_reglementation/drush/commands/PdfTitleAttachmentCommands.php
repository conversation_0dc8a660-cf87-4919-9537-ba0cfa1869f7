<?php

namespace Drush\Commands\import_reglementation;

use Drush\Commands\DrushCommands;
use Dr<PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use <PERSON><PERSON>al\file\Entity\File;
use <PERSON><PERSON><PERSON>\node\Entity\Node;

/**
 * Commandes Drush pour attacher des fichiers PDF aux nœuds par titre exact.
 */
class PdfTitleAttachmentCommands extends DrushCommands {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * Constructs a new PdfTitleAttachmentCommands object.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\File\FileSystemInterface $file_system
   *   The file system service.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    FileSystemInterface $file_system
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->fileSystem = $file_system;
  }

  /**
   * Attache un fichier PDF à un nœud de réglementation en cherchant par titre exact.
   *
   * @param string $file_path
   *   Chemin vers le fichier PDF.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:attach-by-title
   * @aliases pabt
   * @option language Langue du nœud à chercher (fr ou ar, par défaut: auto-détection)
   * @usage pdf:attach-by-title "/path/to/file.pdf"
   *   Attache le fichier PDF au nœud ayant le même titre.
   * @usage pdf:attach-by-title "/path/to/file.pdf" --language=ar
   *   Attache le fichier PDF au nœud arabe ayant le même titre.
   */
  public function attachPdfByTitle($file_path, array $options = ['language' => '']) {
    $language = $options['language'];

    $this->output()->writeln("Attachement du fichier PDF: <info>$file_path</info>");

    // Vérifier que le fichier existe
    if (!file_exists($file_path)) {
      $this->logger()->error("Le fichier $file_path n'existe pas.");
      return;
    }

    // Extraire le titre du nom de fichier (sans extension)
    $filename = basename($file_path);
    $title = preg_replace('/\.(pdf|PDF)$/', '', $filename);

    $this->output()->writeln("Recherche du nœud avec le titre: <comment>$title</comment>");

    // Chercher le nœud par titre
    $node = $this->findNodeByTitle($title, $language);

    if (!$node) {
      $this->logger()->error("Aucun nœud trouvé avec le titre: $title");
      return;
    }

    $this->output()->writeln("Nœud trouvé: <info>ID {$node->id()}, Langue: {$node->language()->getId()}</info>");

    // Attacher le fichier PDF au nœud
    $result = $this->attachPdfToNode($node, $file_path);

    if ($result['success']) {
      $this->logger()->success("Fichier PDF attaché avec succès!");
      $this->output()->writeln("- Nœud ID: <comment>{$node->id()}</comment>");
      $this->output()->writeln("- Fichier ID: <comment>{$result['file_id']}</comment>");
      $this->output()->writeln("- Titre du nœud: <comment>{$node->label()}</comment>");
    } else {
      $this->logger()->error("Erreur lors de l'attachement: {$result['message']}");
    }
  }

  /**
   * Attache tous les fichiers PDF d'un dossier aux nœuds correspondants par titre.
   *
   * @param string $directory_path
   *   Chemin vers le dossier contenant les fichiers PDF.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:attach-directory-by-title
   * @aliases padbt
   * @option language Langue des nœuds à chercher (fr ou ar, par défaut: auto-détection)
   * @option recursive Traiter les sous-dossiers récursivement
   * @usage pdf:attach-directory-by-title "/path/to/pdf/directory"
   *   Attache tous les PDF du dossier aux nœuds ayant les mêmes titres.
   * @usage pdf:attach-directory-by-title "/path/to/pdf/directory" --language=ar --recursive
   *   Attache tous les PDF (récursivement) aux nœuds arabes ayant les mêmes titres.
   */
  public function attachPdfDirectoryByTitle($directory_path, array $options = ['language' => '', 'recursive' => FALSE]) {
    $language = $options['language'];
    $recursive = $options['recursive'];
    $recursion = $recursive ? 'récursif' : 'non-récursif';

    $this->output()->writeln("Traitement du dossier: <info>$directory_path</info> ($recursion)");

    if (!is_dir($directory_path)) {
      $this->logger()->error("Le dossier $directory_path n'existe pas.");
      return;
    }

    $results = [
      'total' => 0,
      'success' => 0,
      'node_found' => 0,
      'file_attached' => 0,
      'errors' => 0,
      'details' => [],
    ];

    // Parcourir les fichiers
    $iterator = $recursive ?
      new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory_path)) :
      new \DirectoryIterator($directory_path);

    foreach ($iterator as $file) {
      if ($file->isDot()) {
        continue;
      }

      if ($file->isFile() && preg_match('/\.pdf$/i', $file->getFilename())) {
        $results['total']++;
        $file_path = $file->getRealPath();
        $filename = $file->getFilename();

        $this->output()->writeln("\nTraitement: <comment>$filename</comment>");

        // Extraire le titre du nom de fichier
        $title = preg_replace('/\.(pdf|PDF)$/', '', $filename);

        // Chercher le nœud par titre
        $node = $this->findNodeByTitle($title, $language);

        if ($node) {
          $results['node_found']++;
          $this->output()->writeln("  Nœud trouvé: ID {$node->id()}");

          // Attacher le fichier
          $attach_result = $this->attachPdfToNode($node, $file_path);

          if ($attach_result['success']) {
            $results['success']++;
            $results['file_attached']++;
            $this->output()->writeln("  <fg=green>✓ Fichier attaché avec succès</fg=green>");
          } else {
            $results['errors']++;
            $this->output()->writeln("  <fg=red>✗ Erreur d'attachement: {$attach_result['message']}</fg=red>");
          }
        } else {
          $results['errors']++;
          $this->output()->writeln("  <fg=red>✗ Aucun nœud trouvé avec le titre: $title</fg=red>");
        }

        $results['details'][] = [
          'filename' => $filename,
          'title' => $title,
          'node_found' => $node ? $node->id() : null,
          'success' => isset($attach_result) ? $attach_result['success'] : false,
        ];
      }
    }

    // Afficher les résultats finaux
    $this->output()->writeln("\n<fg=green>Résultats finaux:</fg=green>");
    $this->output()->writeln("  Total de fichiers traités: <comment>{$results['total']}</comment>");
    $this->output()->writeln("  Succès: <comment>{$results['success']}</comment>");
    $this->output()->writeln("  Nœuds trouvés: <comment>{$results['node_found']}</comment>");
    $this->output()->writeln("  Fichiers attachés: <comment>{$results['file_attached']}</comment>");
    $this->output()->writeln("  Erreurs: <comment>{$results['errors']}</comment>");

    if ($results['success'] > 0) {
      $this->logger()->success("Traitement terminé avec {$results['success']} fichiers attachés!");
    } else {
      $this->logger()->warning("Aucun fichier n'a pu être attaché.");
    }
  }

  /**
   * Trouve un nœud de réglementation par son titre exact.
   *
   * @param string $title
   *   Le titre exact du nœud.
   * @param string $language
   *   La langue du nœud (fr, ar, ou vide pour auto-détection).
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null si non trouvé.
   */
  protected function findNodeByTitle($title, $language = '') {
    try {
      $query = $this->entityTypeManager->getStorage('node')->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation')
        ->condition('title', $title)
        ->range(0, 1);

      // Si une langue spécifique est demandée
      if (!empty($language)) {
        $query->condition('langcode', $language);
      }

      $nids = $query->execute();

      if (!empty($nids)) {
        $nid = reset($nids);
        return $this->entityTypeManager->getStorage('node')->load($nid);
      }

      // Si aucun nœud trouvé et pas de langue spécifiée, essayer les deux langues
      if (empty($language)) {
        foreach (['fr', 'ar'] as $lang) {
          $query = $this->entityTypeManager->getStorage('node')->getQuery()
            ->accessCheck(FALSE)
            ->condition('type', 'reglementation')
            ->condition('title', $title)
            ->condition('langcode', $lang)
            ->range(0, 1);

          $nids = $query->execute();

          if (!empty($nids)) {
            $nid = reset($nids);
            return $this->entityTypeManager->getStorage('node')->load($nid);
          }
        }
      }

      return null;
    } catch (\Exception $e) {
      $this->logger()->error("Erreur lors de la recherche du nœud: {$e->getMessage()}");
      return null;
    }
  }

  /**
   * Attache un fichier PDF au champ field_lien_telechargement d'un nœud.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Le nœud de réglementation.
   * @param string $file_path
   *   Chemin vers le fichier PDF.
   *
   * @return array
   *   Résultat de l'opération avec 'success', 'message' et 'file_id'.
   */
  protected function attachPdfToNode(Node $node, $file_path) {
    $result = [
      'success' => FALSE,
      'message' => '',
      'file_id' => null,
    ];

    try {
      // Vérifier que le fichier existe
      if (!file_exists($file_path)) {
        $result['message'] = "Le fichier n'existe pas: $file_path";
        return $result;
      }

      // Créer le répertoire de destination dans files
      $destination_dir = 'public://reglementation/pdf';
      $this->fileSystem->prepareDirectory(
        $destination_dir,
        FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS
      );

      // Copier le fichier vers le répertoire files de Drupal
      $filename = basename($file_path);
      $destination = $destination_dir . '/' . $filename;

      $file_uri = $this->fileSystem->copy($file_path, $destination);

      if (!$file_uri) {
        $result['message'] = "Impossible de copier le fichier vers: $destination";
        return $result;
      }

      // Créer l'entité File
      $file = File::create([
        'filename' => $filename,
        'uri' => $file_uri,
        'status' => 1,
        'uid' => 1, // Utilisateur admin
      ]);
      $file->save();

      $result['file_id'] = $file->id();

      // Attacher le fichier au nœud
      $node->set('field_lien_telechargement', [
        'target_id' => $file->id(),
        'description' => $filename,
      ]);

      $node->save();

      $result['success'] = TRUE;
      $result['message'] = "Fichier PDF attaché avec succès au nœud {$node->id()}";

    } catch (\Exception $e) {
      $result['message'] = "Erreur lors de l'attachement du fichier: {$e->getMessage()}";
    }

    return $result;
  }

}
