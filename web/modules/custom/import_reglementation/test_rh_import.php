<?php

/**
 * Script de test pour l'importation RH
 * 
 * Ce script peut être exécuté pour tester l'importation RH avec des données d'exemple.
 * 
 * Usage: php test_rh_import.php
 */

// Inclure l'autoloader de Drupal
use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

$autoloader = require_once 'autoload.php';

// Initialiser Drupal
$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

// Créer un fichier CSV de test
$test_csv_content = <<<CSV
Sous secteur,Type,AR,Thème,AR,N° du texte,Intitulé en Arabe,Intitulé en Français,Date De publication,Pièces Jointes
Texte géneral,Dahir,ظهير شريف,Texte géneral,نص عام,TEST.001,ظهير شريف تجريبي,Dahir de test n° 001,01/01/2024,test.pdf
RH,Loi,قانون,Texte géneral - RH,نص عام – قسم الموارد البشرية,TEST.002,قانون تجريبي,Loi de test n° 002,15/06/2024,
Texte géneral,Décret,مرسوم,Administration,الإدارة,TEST.003,مرسوم تجريبي,Décret de test n° 003,30/12/2024,
CSV;

// Créer le fichier temporaire
$temp_file = sys_get_temp_dir() . '/test_rh_import.csv';
file_put_contents($temp_file, $test_csv_content);

echo "Fichier de test créé : $temp_file\n";
echo "Contenu du fichier :\n";
echo $test_csv_content . "\n\n";

try {
  // Obtenir le service d'importation RH
  $importer = \Drupal::service('import_reglementation.csv_importer_rh');
  
  echo "Service d'importation RH chargé avec succès.\n";
  
  // Effectuer l'importation
  echo "Début de l'importation de test...\n";
  $results = $importer->import($temp_file, ',');
  
  // Afficher les résultats
  echo "\n=== RÉSULTATS DE L'IMPORTATION ===\n";
  echo "Succès: " . ($results['success'] ? 'OUI' : 'NON') . "\n";
  echo "Créés: " . $results['created'] . "\n";
  echo "Mis à jour: " . $results['updated'] . "\n";
  echo "Traités: " . $results['processed'] . "\n";
  echo "Erreurs: " . count($results['errors']) . "\n";
  
  if (!empty($results['errors'])) {
    echo "\n=== ERREURS ===\n";
    foreach ($results['errors'] as $error) {
      echo "- $error\n";
    }
  }
  
  if ($results['success']) {
    echo "\n✅ Test d'importation RH réussi !\n";
    
    // Vérifier que les nœuds ont été créés
    echo "\n=== VÉRIFICATION DES NŒUDS CRÉÉS ===\n";
    $query = \Drupal::entityQuery('node')
      ->accessCheck(FALSE)
      ->condition('type', 'reglementation')
      ->condition('field_numero_de_text', 'TEST.', 'STARTS_WITH');
    
    $nids = $query->execute();
    
    if (!empty($nids)) {
      echo "Nœuds de test trouvés : " . count($nids) . "\n";
      
      foreach ($nids as $nid) {
        $node = \Drupal\node\Entity\Node::load($nid);
        if ($node) {
          echo "- ID: $nid, Titre: " . $node->getTitle() . ", Numéro: " . $node->get('field_numero_de_text')->value . "\n";
          
          // Vérifier la traduction arabe
          if ($node->hasTranslation('ar')) {
            $translation = $node->getTranslation('ar');
            echo "  Traduction AR: " . $translation->getTitle() . "\n";
          }
        }
      }
      
      // Nettoyer les nœuds de test
      echo "\n=== NETTOYAGE ===\n";
      $storage = \Drupal::entityTypeManager()->getStorage('node');
      $nodes = $storage->loadMultiple($nids);
      $storage->delete($nodes);
      echo "Nœuds de test supprimés.\n";
      
    } else {
      echo "❌ Aucun nœud de test trouvé !\n";
    }
    
  } else {
    echo "\n❌ Test d'importation RH échoué !\n";
  }
  
} catch (\Exception $e) {
  echo "\n❌ ERREUR LORS DU TEST :\n";
  echo $e->getMessage() . "\n";
  echo "Trace :\n" . $e->getTraceAsString() . "\n";
}

// Nettoyer le fichier temporaire
if (file_exists($temp_file)) {
  unlink($temp_file);
  echo "\nFichier temporaire supprimé.\n";
}

echo "\n=== TEST TERMINÉ ===\n";
