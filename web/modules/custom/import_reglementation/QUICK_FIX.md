# Solution Rapide - Erreur 413 "Request Entity Too Large"

## 🚨 Problème
Vous obtenez cette erreur lors de l'upload de fichiers PDF arabes :
```
413 Request Entity Too Large
```

## ⚡ Solution Rapide (5 minutes)

### 1. Script Automatique (Recommandé)
```bash
# Exécuter le script de configuration automatique
sudo bash web/modules/custom/import_reglementation/scripts/configure_server.sh
```

### 2. Configuration Manuelle

#### A. Modifier PHP (php.ini)
```bash
# Trouver le fichier php.ini
php --ini | grep "Loaded Configuration File"

# Éditer le fichier (remplacez le chemin par le vôtre)
sudo nano /etc/php/8.1/fpm/php.ini
```

Modifiez ces valeurs :
```ini
upload_max_filesize = 100M
post_max_size = 120M
memory_limit = 256M
max_execution_time = 300
max_file_uploads = 20
```

#### B. Modifier Nginx
```bash
sudo nano /etc/nginx/sites-available/votre-site
```

Ajoutez dans le bloc `server {}` :
```nginx
client_max_body_size 100M;
client_body_timeout 300s;
```

#### C. Redémarrer les Services
```bash
sudo systemctl restart php8.1-fpm
sudo systemctl restart nginx
```

## 🔧 Vérification

1. Allez sur : `/admin/content/import-pdf-diagnostic`
2. Vérifiez que tous les paramètres sont en vert
3. Testez avec 2-3 fichiers PDF maximum

## 📋 Conseils pour les PDF Arabes

### ✅ À Faire
- Importer 3-5 fichiers maximum à la fois
- Utiliser le mode simulation d'abord
- Nommer les fichiers avec les numéros de référence (ex: "قانون رقم 31.13.pdf")
- Vérifier les logs : `/admin/reports/dblog`

### ❌ À Éviter
- Uploader plus de 10 fichiers simultanément
- Fichiers PDF de plus de 50MB
- Noms de fichiers sans numéros de référence

## 🆘 Si le Problème Persiste

1. **Réduire le nombre de fichiers** : Essayez avec 1 seul fichier
2. **Vérifier la taille** : Les PDF arabes peuvent être volumineux
3. **Consulter les logs** : 
   ```bash
   tail -f /var/log/nginx/error.log
   tail -f /var/log/php8.1-fpm.log
   ```
4. **Contacter l'administrateur système** avec ces informations :
   - Message d'erreur exact
   - Taille des fichiers
   - Configuration actuelle (visible sur la page diagnostic)

## 📞 Support Rapide

- **Page de diagnostic** : `/admin/content/import-pdf-diagnostic`
- **Logs Drupal** : `/admin/reports/dblog` (filtrer par "import_reglementation")
- **Test de langue** : La page diagnostic teste la détection automatique arabe/français

---

💡 **Astuce** : Commencez toujours par tester avec le mode simulation activé ! 