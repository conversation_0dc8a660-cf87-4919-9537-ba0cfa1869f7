<?php

/**
 * Test simple de l'attachement de fichiers PDF aux nœuds.
 */

// Charger Drupal
$autoloader = require_once '/var/www/html/mtl/vendor/autoload.php';

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

echo "=== Test d'attachement de fichier PDF ===\n\n";

// Créer un fichier de test
$test_file = '/tmp/Test_Arrêté_20.80_Fr.pdf';
file_put_contents($test_file, '%PDF-1.4 Fichier de test pour démonstration');
echo "1. Fichier de test créé: $test_file\n";

// Obtenir le service
$pdf_organizer = \Drupal::service('import_reglementation.pdf_organizer');

// Traiter le fichier
echo "\n2. Traitement du fichier...\n";
$result = $pdf_organizer->processPdfFile($test_file);

echo "\n3. Résultats:\n";
echo "   - Succès: " . ($result['success'] ? 'Oui' : 'Non') . "\n";
echo "   - Message: {$result['message']}\n";

if (!empty($result['file_info'])) {
    $info = $result['file_info'];
    echo "   - Type détecté: {$info['type']}\n";
    echo "   - Numéro détecté: {$info['numero']}\n";
    echo "   - Langue détectée: {$info['langue']}\n";
}

if ($result['node_found']) {
    echo "   - Nœud trouvé: ID {$result['node_id']}\n";
} else {
    echo "   - Aucun nœud correspondant trouvé\n";
}

if ($result['file_attached']) {
    echo "   - Fichier attaché: ID {$result['file_id']}\n";
} else {
    echo "   - Fichier non attaché\n";
}

// Nettoyer
if (file_exists($test_file)) {
    unlink($test_file);
    echo "\n4. Fichier de test supprimé.\n";
}

echo "\n=== Fin du test ===\n";
