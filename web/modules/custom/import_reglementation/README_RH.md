# Import de Réglementations RH et Textes Généraux

Ce module contient un script d'importation spécialement adapté pour le format CSV "RH et textes généraux".

## Format CSV Supporté

Le script `CsvImporterRH` est conçu pour traiter les fichiers CSV avec la structure suivante :

### Colonnes Requises

1. **Sous secteur** - Le sous-secteur de la réglementation
2. **Type** - Le type de réglementation (Dahir, Loi, Décret, Arrêté, Circulaire, etc.)
3. **AR** - Le type en arabe (première colonne AR)
4. **Thème** - Le thème/domaine de la réglementation
5. **AR** - Le thème en arabe (deuxième colonne AR)
6. **N° du texte** - Le numéro unique du texte réglementaire
7. **Intitulé en Arabe** - Le titre en arabe
8. **Intitulé en Français** - Le titre en français
9. **Date De publication** - La date de publication
10. **Pièces Jointes** - Les fichiers joints (optionnel)

### Exemple de Structure CSV

```csv
Sous secteur,Type,AR,Thème,AR,N° du texte,Intitulé en Arabe,Intitulé en Français,Date De publication,Pièces Jointes
Texte géneral,Dahir,ظهير شريف,Texte géneral,نص عام,1.59.413,ظهير شريف رقم 1.59.413,Dahir n° 1-59-413 portant approbation du texte du Code pénal,26/11/1962,nom du fichier.pdf
RH,Loi,قانون,Texte géneral - RH,نص عام – قسم الموارد البشرية,011.71,قانون رقم 011.71,Loi n° 011-71 instituant un régime de pensions civiles,31/12/1971,
```

## Utilisation

### 1. Via l'Interface Web

Accédez à : `/admin/content/import-reglementation-rh`

- Sélectionnez votre fichier CSV
- Choisissez le délimiteur (généralement `,`)
- Utilisez le mode aperçu pour valider avant l'importation
- Cliquez sur "Importer"

### 2. Via Drush

```bash
# Importer un fichier CSV RH
vendor/bin/drush import-reglementation:csv-rh /path/to/your/file.csv

# Ou avec l'alias court
vendor/bin/drush irh /path/to/your/file.csv

# Avec un délimiteur personnalisé
vendor/bin/drush irh /path/to/your/file.csv --delimiter=";"

# Valider un fichier avant importation
vendor/bin/drush import-reglementation:validate-rh /path/to/your/file.csv

# Ou avec l'alias court
vendor/bin/drush vrh /path/to/your/file.csv
```

## Fonctionnalités

### Mapping des Champs

- **Titre** : Utilise "Intitulé en Français", fallback sur "N° du texte" si vide
- **Numéro de texte** : Champ unique pour identifier les doublons
- **Type** : Créé automatiquement dans la taxonomie `type_reglementation`
- **Secteur** : Utilise "Sous secteur", créé dans la taxonomie `modes_de_transport`
- **Thème** : Créé automatiquement dans la taxonomie `domaines_d_activites`
- **Date** : Support pour différents formats de date

### Gestion des Traductions

- **Français** : Langue principale du contenu
- **Arabe** : Traduction automatique créée si "Intitulé en Arabe" est fourni
- **Taxonomies** : Les termes arabes sont ajoutés comme traductions

### Gestion des Doublons

- Détection basée sur le champ "N° du texte"
- Mise à jour automatique des entrées existantes
- Création de nouvelles entrées pour les numéros non trouvés

### Formats de Date Supportés

- `DD/MM/YYYY` (ex: 26/11/1962)
- `DD.MM.YYYY` (ex: 26.11.1962)
- `YYYY-MM-DD` (ex: 1962-11-26)
- `DD-MM-YYYY` (ex: 26-11-1962)

## Différences avec l'Importateur Standard

Le `CsvImporterRH` diffère de l'importateur standard (`CsvImporter`) par :

1. **Structure CSV** : Adapté au format "RH et textes généraux"
2. **Colonnes** : Support pour "Sous secteur" au lieu de "Secteur"
3. **Colonnes AR multiples** : Gestion des colonnes AR dupliquées
4. **Validation** : Validation spécifique au nouveau format
5. **Simplicité** : Code plus simple et focalisé sur ce format spécifique

## Logs et Débogage

Les logs sont disponibles dans :
- **Drupal logs** : `/admin/reports/dblog` (filtrer par "import_reglementation")
- **Fichiers logs** : Selon la configuration de votre serveur

Types de logs générés :
- `notice` : Opérations normales (création, mise à jour)
- `warning` : Problèmes non bloquants (formats de date, données manquantes)
- `error` : Erreurs bloquantes (fichier non trouvé, erreurs de base de données)

## Dépannage

### Erreurs Communes

1. **"Colonnes manquantes"** : Vérifiez que votre CSV contient toutes les colonnes requises
2. **"Format de date non reconnu"** : Utilisez un des formats supportés
3. **"Impossible d'ouvrir le fichier"** : Vérifiez les permissions et l'encodage du fichier
4. **"Erreur de base de données"** : Vérifiez que les vocabulaires de taxonomie existent

### Conseils

- Utilisez toujours le mode aperçu avant l'importation
- Sauvegardez votre base de données avant les grosses importations
- Vérifiez l'encodage de votre fichier CSV (UTF-8 recommandé)
- Testez avec un petit échantillon avant l'importation complète

## Support

Pour les problèmes spécifiques à ce script, consultez les logs Drupal et vérifiez :
1. La structure de votre fichier CSV
2. Les permissions de fichiers
3. L'existence des vocabulaires de taxonomie requis
4. La configuration des champs du type de contenu "reglementation"
