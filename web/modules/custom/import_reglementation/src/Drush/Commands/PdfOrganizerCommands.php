<?php

namespace Drupal\import_reglementation\Drush\Commands;

use Dr<PERSON>al\import_reglementation\Service\PdfOrganizer;
use Drush\Commands\DrushCommands;
use Drush\Commands\AutowireTrait;

/**
 * Commandes Drush pour organiser les fichiers PDF.
 */
class PdfOrganizerCommands extends DrushCommands {

  use AutowireTrait;

  /**
   * Le service PdfOrganizer.
   *
   * @var \Drupal\import_reglementation\Service\PdfOrganizer
   */
  protected $pdfOrganizer;

  /**
   * Obtient le service PdfOrganizer via Autowire.
   *
   * @return \Drupal\import_reglementation\Service\PdfOrganizer
   */
  protected function getPdfOrganizer(): PdfOrganizer {
    if (!$this->pdfOrganizer) {
      $this->pdfOrganizer = \Drupal::service('import_reglementation.pdf_organizer');
    }
    return $this->pdfOrganizer;
  }

  /**
   * Analyse un nom de fichier PDF pour extraire les informations.
   *
   * @param string $filename
   *   Le nom du fichier à analyser.
   *
   * @command pdf:analyze
   * @aliases pdf-analyze
   * @usage pdf:analyze "Arrêté 20.80 Fr.pdf"
   *   Analyse le nom de fichier spécifié.
   */
  public function analyzeFilename($filename) {
    $this->output()->writeln("Analyse du fichier: <info>$filename</info>");

    $result = $this->getPdfOrganizer()->analyzeFilename($filename);

    if ($result['valid']) {
      $this->output()->writeln('<fg=green>✓ Analyse réussie</fg=green>');
      $this->output()->writeln("  Type de réglementation: <comment>{$result['type']}</comment>");
      $this->output()->writeln("  Numéro de texte: <comment>{$result['numero']}</comment>");
      $this->output()->writeln("  Langue: <comment>{$result['langue']}</comment>");

      $destination_path = $this->getPdfOrganizer()->generateDestinationPath($result);
      $this->output()->writeln("  Chemin de destination: <comment>$destination_path</comment>");
    } else {
      $this->output()->writeln('<fg=red>✗ Impossible d\'analyser le nom de fichier</fg=red>');
    }
  }

  /**
   * Trouve le nœud de réglementation correspondant à un fichier PDF.
   *
   * @param string $filename
   *   Nom du fichier PDF à analyser.
   *
   * @command pdf:find-node
   * @aliases pdf-find-node
   * @usage pdf:find-node "Arrêté 20.80 Fr.pdf"
   *   Trouve le nœud correspondant au fichier PDF.
   */
  public function findNode($filename) {
    $this->output()->writeln("Recherche du nœud pour: <info>$filename</info>");

    // Analyser le fichier
    $file_info = $this->getPdfOrganizer()->analyzeFilename($filename);

    if (!$file_info['valid']) {
      $this->output()->writeln('<fg=red>✗ Impossible d\'analyser le nom de fichier</fg=red>');
      return;
    }

    $this->output()->writeln("Informations extraites:");
    $this->output()->writeln("  - Type: <comment>{$file_info['type']}</comment>");
    $this->output()->writeln("  - Numéro: <comment>{$file_info['numero']}</comment>");
    $this->output()->writeln("  - Langue: <comment>{$file_info['langue']}</comment>");

    // Chercher le nœud
    $node = $this->getPdfOrganizer()->findCorrespondingNode($file_info);

    if ($node) {
      $this->output()->writeln("<fg=green>✓ Nœud trouvé:</fg=green>");
      $this->output()->writeln("  - ID: <comment>{$node->id()}</comment>");
      $this->output()->writeln("  - Titre: <comment>{$node->label()}</comment>");
      $this->output()->writeln("  - Langue: <comment>{$node->language()->getId()}</comment>");
      $this->output()->writeln("  - URL: <comment>/node/{$node->id()}</comment>");
    } else {
      $this->output()->writeln('<fg=red>✗ Aucun nœud correspondant trouvé</fg=red>');
    }
  }

  /**
   * Traite un fichier PDF complet : trouve le nœud et attache le fichier.
   *
   * @param string $file_path
   *   Chemin vers le fichier PDF.
   *
   * @command pdf:process
   * @aliases pdf-process
   * @usage pdf:process "/path/to/Arrêté 20.80 Fr.pdf"
   *   Traite le fichier PDF et l'attache au nœud correspondant.
   */
  public function processFile($file_path) {
    $this->output()->writeln("Traitement du fichier: <info>$file_path</info>");

    if (!file_exists($file_path)) {
      $this->output()->writeln('<fg=red>✗ Le fichier n\'existe pas</fg=red>');
      return;
    }

    $result = $this->getPdfOrganizer()->processPdfFile($file_path);

    $this->output()->writeln("\n<fg=yellow>Résultats du traitement:</fg=yellow>");

    // Informations du fichier
    if (!empty($result['file_info'])) {
      $info = $result['file_info'];
      $this->output()->writeln("📄 <comment>Analyse du fichier:</comment>");
      $this->output()->writeln("   - Type: <info>{$info['type']}</info>");
      $this->output()->writeln("   - Numéro: <info>{$info['numero']}</info>");
      $this->output()->writeln("   - Langue: <info>{$info['langue']}</info>");
    }

    // Recherche du nœud
    if ($result['node_found']) {
      $this->output()->writeln("🔍 <fg=green>Nœud trouvé: ID {$result['node_id']}</fg=green>");
    } else {
      $this->output()->writeln("🔍 <fg=red>Aucun nœud correspondant trouvé</fg=red>");
    }

    // Attachement du fichier
    if ($result['file_attached']) {
      $this->output()->writeln("📎 <fg=green>Fichier attaché avec succès (File ID: {$result['file_id']})</fg=green>");
    }

    // Résultat final
    if ($result['success']) {
      $this->output()->writeln("\n<fg=green>✓ Traitement réussi</fg=green>");
      $this->output()->writeln("Message: <comment>{$result['message']}</comment>");
    } else {
      $this->output()->writeln("\n<fg=red>✗ Traitement échoué</fg=red>");
      $this->output()->writeln("Message: <comment>{$result['message']}</comment>");
    }
  }

  /**
   * Traite tous les fichiers PDF d'un dossier et les attache aux nœuds correspondants.
   *
   * @param string $source_directory
   *   Dossier contenant les fichiers PDF.
   * @param array $options
   *   Options de la commande.
   *
   * @command pdf:process-directory
   * @aliases pdf-process-dir
   * @option recursive Traiter les sous-dossiers récursivement
   * @usage pdf:process-directory /path/to/pdf/directory
   *   Traite tous les PDF du dossier.
   * @usage pdf:process-directory /path/to/pdf/directory --recursive
   *   Traite tous les PDF du dossier et sous-dossiers.
   */
  public function processDirectory($source_directory, array $options = ['recursive' => FALSE]) {
    $recursive = $options['recursive'];
    $recursion = $recursive ? 'récursif' : 'non-récursif';

    $this->output()->writeln("Traitement du dossier: <info>$source_directory</info> ($recursion)");

    if (!is_dir($source_directory)) {
      $this->output()->writeln('<fg=red>✗ Le dossier n\'existe pas</fg=red>');
      return;
    }

    $results = [
      'total' => 0,
      'success' => 0,
      'node_found' => 0,
      'file_attached' => 0,
      'errors' => 0,
      'details' => [],
    ];

    $iterator = $recursive ?
      new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($source_directory)) :
      new \DirectoryIterator($source_directory);

    foreach ($iterator as $file) {
      // Pour RecursiveIteratorIterator, vérifier différemment
      if ($recursive) {
        if ($file->getFilename() === '.' || $file->getFilename() === '..') {
          continue;
        }
      } else {
        if ($file->isDot()) {
          continue;
        }
      }

      if ($file->isFile() && preg_match('/\.pdf$/i', $file->getFilename())) {
        $results['total']++;
        $file_path = $file->getRealPath();

        $this->output()->writeln("\nTraitement: <comment>{$file->getFilename()}</comment>");

        $result = $this->getPdfOrganizer()->processPdfFile($file_path);

        if ($result['success']) {
          $results['success']++;
          $this->output()->writeln("  <fg=green>✓ Succès</fg=green>");
        } else {
          $results['errors']++;
          $this->output()->writeln("  <fg=red>✗ Erreur: {$result['message']}</fg=red>");
        }

        if ($result['node_found']) {
          $results['node_found']++;
        }

        if ($result['file_attached']) {
          $results['file_attached']++;
        }

        $results['details'][] = [
          'file' => $file->getFilename(),
          'result' => $result,
        ];
      }
    }

    $this->output()->writeln("\n<fg=green>Résultats finaux:</fg=green>");
    $this->output()->writeln("  Total de fichiers traités: <comment>{$results['total']}</comment>");
    $this->output()->writeln("  Succès: <comment>{$results['success']}</comment>");
    $this->output()->writeln("  Nœuds trouvés: <comment>{$results['node_found']}</comment>");
    $this->output()->writeln("  Fichiers attachés: <comment>{$results['file_attached']}</comment>");
    $this->output()->writeln("  Erreurs: <comment>{$results['errors']}</comment>");
  }

  /**
   * Affiche la structure actuelle des dossiers PDF.
   *
   * @command pdf:structure
   * @aliases pdf-structure
   * @usage pdf:structure
   *   Affiche la structure des dossiers PDF.
   */
  public function showStructure() {
    $this->output()->writeln("<fg=green>Structure des dossiers PDF:</fg=green>");

    $pdf_base_path = \Drupal::service('extension.list.module')->getPath('import_reglementation') . '/pdf';
    $this->output()->writeln("Chemin de base: <comment>$pdf_base_path</comment>\n");

    $this->displayDirectoryStructure($pdf_base_path, 0);
  }

  /**
   * Affiche la structure d'un dossier de manière récursive.
   *
   * @param string $path
   *   Chemin du dossier.
   * @param int $level
   *   Niveau de profondeur.
   */
  protected function displayDirectoryStructure($path, $level = 0) {
    if (!is_dir($path)) {
      return;
    }

    $indent = str_repeat('  ', $level);
    $items = scandir($path);

    foreach ($items as $item) {
      if ($item === '.' || $item === '..') {
        continue;
      }

      $item_path = $path . '/' . $item;

      if (is_dir($item_path)) {
        $this->output()->writeln($indent . "📁 <info>$item/</info>");
        if ($level < 3) { // Limiter la profondeur
          $this->displayDirectoryStructure($item_path, $level + 1);
        }
      } else {
        $this->output()->writeln($indent . "📄 <comment>$item</comment>");
      }
    }
  }
}
