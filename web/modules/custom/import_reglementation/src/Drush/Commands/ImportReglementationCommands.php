<?php

namespace Drupal\import_reglementation\Drush\Commands;

use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\file\Entity\File;
use Drupal\node\Entity\Node;
use Drush\Attributes as CLI;
use Drush\Commands\AutowireTrait;
use Drush\Commands\DrushCommands;


/**
 * Commandes Drush pour importer des fichiers PDF vers le champ field_telechargement.
 */
final class ImportReglementationCommands extends DrushCommands {

  use AutowireTrait;

  /**
   * Constructs an ImportReglementationCommands object.
   */
  public function __construct(
    private readonly EntityTypeManagerInterface $entityTypeManager,
    private readonly FileSystemInterface $fileSystem,
    private readonly LoggerChannelFactoryInterface $loggerFactory,
  ) {
    parent::__construct();
  }

  /**
   * Importe un fichier PDF vers le champ field_lien_telechargement d'un nœud de réglementation.
   */
  #[CLI\Command(name: 'pdf:import-single', aliases: ['pis'])]
  #[CLI\Argument(name: 'file_path', description: 'Chemin vers le fichier PDF à importer')]
  #[CLI\Option(name: 'language', description: 'Langue du nœud à chercher (fr ou ar, par défaut: auto-détection)')]
  #[CLI\Usage(name: 'pdf:import-single "/path/to/file.pdf"', description: 'Importe le fichier PDF vers le nœud ayant le même titre')]
  #[CLI\Usage(name: 'pdf:import-single "/path/to/file.pdf" --language=ar', description: 'Importe le fichier PDF vers le nœud arabe ayant le même titre')]
  public function importSinglePdf($file_path, $options = ['language' => '']) {
    $language = $options['language'];

    $this->output()->writeln("Import du fichier PDF: <info>$file_path</info>");

    // Vérifier que le fichier existe
    if (!file_exists($file_path)) {
      $this->logger()->error("Le fichier $file_path n'existe pas.");
      return;
    }

    // Extraire le titre du nom de fichier (sans extension)
    $filename = basename($file_path);
    $title = preg_replace('/\.(pdf|PDF)$/', '', $filename);

    $this->output()->writeln("Recherche du nœud avec le titre: <comment>$title</comment>");

    // Détecter la langue du titre si pas spécifiée
    if (empty($language)) {
      $language = $this->detectLanguage($title);
      $this->output()->writeln("  Langue détectée: <comment>$language</comment>");
    }

    // Chercher le nœud par titre
    $node = $this->findNodeByTitle($title, $language);

    if (!$node) {
      $this->logger()->error("Aucun nœud trouvé avec le titre: $title");
      return;
    }

    $this->output()->writeln("Nœud trouvé: <info>ID {$node->id()}, Langue: {$node->language()->getId()}</info>");

    // Importer le fichier PDF au nœud
    $result = $this->importPdfToNode($node, $file_path);

    if ($result['success']) {
      $this->logger()->success("Fichier PDF importé avec succès!");
      $this->output()->writeln("- Nœud ID: <comment>{$node->id()}</comment>");
      $this->output()->writeln("- Fichier ID: <comment>{$result['file_id']}</comment>");
      $this->output()->writeln("- Titre du nœud: <comment>{$node->label()}</comment>");
    } else {
      $this->logger()->error("Erreur lors de l'import: {$result['message']}");
    }
  }

  /**
   * Importe tous les fichiers PDF d'un dossier vers les nœuds correspondants.
   */
  #[CLI\Command(name: 'pdf:import-directory', aliases: ['pid'])]
  #[CLI\Argument(name: 'directory_path', description: 'Chemin vers le dossier contenant les fichiers PDF')]
  #[CLI\Option(name: 'language', description: 'Langue des nœuds à chercher (fr ou ar, par défaut: auto-détection)')]
  #[CLI\Option(name: 'recursive', description: 'Traiter les sous-dossiers récursivement')]
  #[CLI\Option(name: 'dry-run', description: 'Mode simulation - affiche ce qui serait fait sans effectuer les modifications')]
  #[CLI\Usage(name: 'pdf:import-directory "/path/to/pdf/directory"', description: 'Importe tous les PDF du dossier vers les nœuds ayant les mêmes titres')]
  #[CLI\Usage(name: 'pdf:import-directory "/path/to/pdf/directory" --language=ar --recursive', description: 'Importe tous les PDF (récursivement) vers les nœuds arabes ayant les mêmes titres')]
  #[CLI\Usage(name: 'pdf:import-directory "/path/to/pdf/directory" --dry-run', description: 'Simule l\'import sans effectuer les modifications')]
  public function importPdfDirectory($directory_path, $options = ['language' => '', 'recursive' => FALSE, 'dry-run' => FALSE]) {
    $language = $options['language'];
    $recursive = $options['recursive'];
    $dry_run = $options['dry-run'];
    $recursion = $recursive ? 'récursif' : 'non-récursif';
    $mode = $dry_run ? ' (MODE SIMULATION)' : '';

    $this->output()->writeln("Traitement du dossier: <info>$directory_path</info> ($recursion)$mode");

    if (!is_dir($directory_path)) {
      $this->logger()->error("Le dossier $directory_path n'existe pas.");
      return;
    }

    $results = [
      'total' => 0,
      'success' => 0,
      'node_found' => 0,
      'file_imported' => 0,
      'errors' => 0,
      'skipped' => 0,
      'details' => [],
    ];

    // Parcourir les fichiers
    $iterator = $recursive ?
      new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory_path)) :
      new \DirectoryIterator($directory_path);

    foreach ($iterator as $file) {
      if ($file->isDot()) {
        continue;
      }

      if ($file->isFile() && preg_match('/\.pdf$/i', $file->getFilename())) {
        $results['total']++;
        $file_path = $file->getRealPath();
        $filename = $file->getFilename();

        $this->output()->writeln("\nTraitement: <comment>$filename</comment>");

        // Extraire le titre du nom de fichier
        $title = preg_replace('/\.(pdf|PDF)$/', '', $filename);

        // Détecter la langue du titre si pas spécifiée
        $detected_language = empty($language) ? $this->detectLanguage($title) : $language;
        if (empty($language)) {
          $this->output()->writeln("  Langue détectée: <comment>$detected_language</comment>");
        }

        // Chercher le nœud par titre
        $node = $this->findNodeByTitle($title, $detected_language);

        if ($node) {
          $results['node_found']++;
          $this->output()->writeln("  Nœud trouvé: ID {$node->id()}");

          // Vérifier si le nœud a déjà un fichier dans field_lien_telechargement
          if (!$node->get('field_lien_telechargement')->isEmpty()) {
            $results['skipped']++;
            $this->output()->writeln("  <fg=yellow>⚠ Nœud a déjà un fichier - ignoré</fg=yellow>");
            continue;
          }

          if ($dry_run) {
            $results['success']++;
            $this->output()->writeln("  <fg=blue>🔍 SIMULATION: Fichier serait importé</fg=blue>");
          } else {
            // Importer le fichier
            $import_result = $this->importPdfToNode($node, $file_path);

            if ($import_result['success']) {
              $results['success']++;
              $results['file_imported']++;
              $this->output()->writeln("  <fg=green>✓ Fichier importé avec succès</fg=green>");
            } else {
              $results['errors']++;
              $this->output()->writeln("  <fg=red>✗ Erreur d'import: {$import_result['message']}</fg=red>");
            }
          }
        } else {
          $results['errors']++;
          $this->output()->writeln("  <fg=red>✗ Aucun nœud trouvé avec le titre: $title</fg=red>");
        }

        $results['details'][] = [
          'filename' => $filename,
          'title' => $title,
          'node_found' => $node ? $node->id() : null,
          'success' => isset($import_result) ? $import_result['success'] : ($dry_run && $node ? true : false),
        ];
      }
    }

    // Afficher les résultats finaux
    $this->displayResults($results, $dry_run);
  }

  /**
   * Trouve un nœud de réglementation par son titre exact.
   *
   * @param string $title
   *   Le titre exact du nœud.
   * @param string $language
   *   La langue du nœud (fr, ar, ou vide pour auto-détection).
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null si non trouvé.
   */
  protected function findNodeByTitle($title, $language = '') {
    try {
      $query = $this->entityTypeManager->getStorage('node')->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation')
        ->condition('title', $title)
        ->range(0, 1);

      // Si une langue spécifique est demandée
      if (!empty($language)) {
        $query->condition('langcode', $language);
      }

      $nids = $query->execute();

      if (!empty($nids)) {
        $nid = reset($nids);
        return $this->entityTypeManager->getStorage('node')->load($nid);
      }

      // Si aucun nœud trouvé et pas de langue spécifiée, essayer les deux langues
      if (empty($language)) {
        foreach (['fr', 'ar'] as $lang) {
          $query = $this->entityTypeManager->getStorage('node')->getQuery()
            ->accessCheck(FALSE)
            ->condition('type', 'reglementation')
            ->condition('title', $title)
            ->condition('langcode', $lang)
            ->range(0, 1);

          $nids = $query->execute();

          if (!empty($nids)) {
            $nid = reset($nids);
            return $this->entityTypeManager->getStorage('node')->load($nid);
          }
        }
      }

      return null;
    } catch (\Exception $e) {
      $this->logger()->error("Erreur lors de la recherche du nœud: {$e->getMessage()}");
      return null;
    }
  }

  /**
   * Importe un fichier PDF au champ field_lien_telechargement d'un nœud.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Le nœud de réglementation.
   * @param string $file_path
   *   Chemin vers le fichier PDF.
   *
   * @return array
   *   Résultat de l'opération avec 'success', 'message' et 'file_id'.
   */
  protected function importPdfToNode(Node $node, $file_path) {
    $result = [
      'success' => FALSE,
      'message' => '',
      'file_id' => null,
    ];

    try {
      // Vérifier que le fichier existe
      if (!file_exists($file_path)) {
        $result['message'] = "Le fichier n'existe pas: $file_path";
        return $result;
      }

      // Créer le répertoire de destination dans files
      $destination_dir = 'public://reglementation/pdf';
      $this->fileSystem->prepareDirectory(
        $destination_dir,
        FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS
      );

      // Copier le fichier vers le répertoire files de Drupal
      $filename = basename($file_path);
      $destination = $destination_dir . '/' . $filename;

      $file_uri = $this->fileSystem->copy($file_path, $destination);

      if (!$file_uri) {
        $result['message'] = "Impossible de copier le fichier vers: $destination";
        return $result;
      }

      // Créer l'entité File
      $file = File::create([
        'filename' => $filename,
        'uri' => $file_uri,
        'status' => 1,
        'uid' => 1, // Utilisateur admin
      ]);
      $file->save();

      $result['file_id'] = $file->id();

      // Attacher le fichier au nœud dans le champ field_lien_telechargement
      $node->set('field_lien_telechargement', [
        'target_id' => $file->id(),
        'description' => $filename,
      ]);

      $node->save();

      $result['success'] = TRUE;
      $result['message'] = "Fichier PDF importé avec succès au nœud {$node->id()}";

      // Log de l'opération
      $this->loggerFactory->get('import_reglementation')->info(
        'PDF importé: @filename vers nœud @nid (@title)',
        [
          '@filename' => $filename,
          '@nid' => $node->id(),
          '@title' => $node->label(),
        ]
      );

    } catch (\Exception $e) {
      $result['message'] = "Erreur lors de l'import du fichier: {$e->getMessage()}";
      $this->loggerFactory->get('import_reglementation')->error(
        'Erreur import PDF: @message',
        ['@message' => $e->getMessage()]
      );
    }

    return $result;
  }

  /**
   * Affiche les résultats finaux de l'opération.
   *
   * @param array $results
   *   Les résultats de l'opération.
   * @param bool $dry_run
   *   Si c'est un mode simulation.
   */
  protected function displayResults(array $results, $dry_run = FALSE) {
    $mode_text = $dry_run ? ' (SIMULATION)' : '';

    $this->output()->writeln("\n<fg=green>Résultats finaux$mode_text:</fg=green>");
    $this->output()->writeln("  Total de fichiers traités: <comment>{$results['total']}</comment>");
    $this->output()->writeln("  Succès: <comment>{$results['success']}</comment>");
    $this->output()->writeln("  Nœuds trouvés: <comment>{$results['node_found']}</comment>");

    if (!$dry_run) {
      $this->output()->writeln("  Fichiers importés: <comment>{$results['file_imported']}</comment>");
    }

    $this->output()->writeln("  Ignorés (déjà un fichier): <comment>{$results['skipped']}</comment>");
    $this->output()->writeln("  Erreurs: <comment>{$results['errors']}</comment>");

    if ($results['success'] > 0) {
      $action = $dry_run ? 'seraient importés' : 'importés';
      $this->logger()->success("Traitement terminé avec {$results['success']} fichiers $action!");
    } else {
      $this->logger()->warning("Aucun fichier n'a pu être traité.");
    }
  }

  /**
   * Détecte la langue d'un titre basé sur les caractères utilisés.
   *
   * @param string $title
   *   Le titre à analyser.
   *
   * @return string
   *   Le code de langue détecté ('ar' pour arabe, 'fr' pour français).
   */
  protected function detectLanguage($title) {
    // Compter les caractères arabes (plage Unicode pour l'arabe)
    $arabic_chars = preg_match_all('/[\x{0600}-\x{06FF}\x{0750}-\x{077F}\x{08A0}-\x{08FF}\x{FB50}-\x{FDFF}\x{FE70}-\x{FEFF}]/u', $title);

    // Compter les caractères latins (français)
    $latin_chars = preg_match_all('/[a-zA-ZÀ-ÿ]/u', $title);

    // Si plus de caractères arabes que latins, c'est de l'arabe
    if ($arabic_chars > $latin_chars) {
      return 'ar';
    }

    // Sinon, c'est du français par défaut
    return 'fr';
  }

}
