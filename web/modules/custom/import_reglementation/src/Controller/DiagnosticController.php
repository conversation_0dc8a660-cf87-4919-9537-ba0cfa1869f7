<?php

namespace <PERSON><PERSON>al\import_reglementation\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\Core\File\FileSystemInterface;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;

/**
 * Contrôleur de diagnostic pour l'import de PDF.
 */
class DiagnosticController extends ControllerBase {

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * Constructs a new DiagnosticController object.
   */
  public function __construct(FileSystemInterface $file_system) {
    $this->fileSystem = $file_system;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('file_system')
    );
  }

  /**
   * Page de diagnostic des configurations.
   */
  public function diagnostic() {
    $build = [];

    // Titre de la page
    $build['title'] = [
      '#markup' => '<h1>' . $this->t('Diagnostic de configuration pour l\'import PDF') . '</h1>',
    ];

    // Configuration PHP
    $build['php_config'] = [
      '#type' => 'details',
      '#title' => $this->t('Configuration PHP'),
      '#open' => TRUE,
    ];

    $php_settings = [
      'upload_max_filesize' => ini_get('upload_max_filesize'),
      'post_max_size' => ini_get('post_max_size'),
      'memory_limit' => ini_get('memory_limit'),
      'max_execution_time' => ini_get('max_execution_time'),
      'max_file_uploads' => ini_get('max_file_uploads'),
      'max_input_vars' => ini_get('max_input_vars'),
    ];

    $php_table = [
      '#type' => 'table',
      '#header' => [$this->t('Paramètre'), $this->t('Valeur actuelle'), $this->t('Recommandé'), $this->t('Statut')],
      '#rows' => [],
    ];

    $recommendations = [
      'upload_max_filesize' => '100M',
      'post_max_size' => '120M',
      'memory_limit' => '256M',
      'max_execution_time' => '300',
      'max_file_uploads' => '20',
      'max_input_vars' => '3000',
    ];

    foreach ($php_settings as $setting => $value) {
      $recommended = $recommendations[$setting] ?? 'N/A';
      $status = $this->checkPhpSetting($setting, $value, $recommended);
      
      $php_table['#rows'][] = [
        $setting,
        $value,
        $recommended,
        [
          'data' => $status['message'],
          'style' => 'color: ' . $status['color'],
        ],
      ];
    }

    $build['php_config']['table'] = $php_table;

    // Test de répertoires
    $build['directories'] = [
      '#type' => 'details',
      '#title' => $this->t('Répertoires et permissions'),
      '#open' => TRUE,
    ];

    $directories_to_check = [
      'public://import_reglementation_uploads',
      'public://reglementation/pdf',
      'temporary://',
    ];

    $dir_table = [
      '#type' => 'table',
      '#header' => [$this->t('Répertoire'), $this->t('Chemin réel'), $this->t('Existe'), $this->t('Écriture'), $this->t('Statut')],
      '#rows' => [],
    ];

    foreach ($directories_to_check as $dir) {
      $real_path = $this->fileSystem->realpath($dir);
      $exists = $real_path && is_dir($real_path);
      $writable = $exists && is_writable($real_path);
      
      $status_color = ($exists && $writable) ? 'green' : 'red';
      $status_text = ($exists && $writable) ? $this->t('OK') : $this->t('Problème');
      
      $dir_table['#rows'][] = [
        $dir,
        $real_path ?: $this->t('Non résolu'),
        $exists ? $this->t('Oui') : $this->t('Non'),
        $writable ? $this->t('Oui') : $this->t('Non'),
        [
          'data' => $status_text,
          'style' => 'color: ' . $status_color,
        ],
      ];
    }

    $build['directories']['table'] = $dir_table;

    // Test de détection de langue
    $build['language_test'] = [
      '#type' => 'details',
      '#title' => $this->t('Test de détection de langue'),
      '#open' => FALSE,
    ];

    $test_titles = [
      'قانون رقم 31.13 المتعلق بالحق في الحصول على المعلومات',
      'Loi n° 1.18.15 relative à la protection des données personnelles',
      'ظهير شريف رقم 2.21.968 بتنفيذ القانون رقم 72.20',
      'Décret n° 2-20-742 du 4 novembre 2020',
    ];

    $pdf_processor = \Drupal::service('import_reglementation.pdf_zip_processor');
    
    $lang_table = [
      '#type' => 'table',
      '#header' => [$this->t('Titre de test'), $this->t('Langue détectée')],
      '#rows' => [],
    ];

    foreach ($test_titles as $title) {
      $detected_lang = $pdf_processor->detectLanguage($title);
      $lang_table['#rows'][] = [
        $title,
        $detected_lang === 'ar' ? $this->t('Arabe') : $this->t('Français'),
      ];
    }

    $build['language_test']['table'] = $lang_table;

    // Recommandations
    $build['recommendations'] = [
      '#type' => 'details',
      '#title' => $this->t('Recommandations'),
      '#open' => TRUE,
    ];

    $recommendations_list = [
      $this->t('Pour résoudre l\'erreur 413, augmentez les valeurs PHP en rouge ci-dessus'),
      $this->t('Redémarrez PHP-FPM et votre serveur web après les modifications'),
      $this->t('Testez avec de petits lots de fichiers (3-5 PDFs maximum)'),
      $this->t('Utilisez le mode simulation avant l\'import réel'),
      $this->t('Surveillez les logs Drupal pour diagnostiquer les problèmes'),
    ];

    $build['recommendations']['list'] = [
      '#theme' => 'item_list',
      '#items' => $recommendations_list,
    ];

    // Actions rapides
    $build['actions'] = [
      '#type' => 'details',
      '#title' => $this->t('Actions rapides'),
      '#open' => TRUE,
    ];

    $build['actions']['create_dirs'] = [
      '#type' => 'submit',
      '#value' => $this->t('Créer les répertoires manquants'),
      '#submit' => ['::createDirectories'],
    ];

    return $build;
  }

  /**
   * Vérifie un paramètre PHP.
   */
  protected function checkPhpSetting($setting, $current, $recommended) {
    if ($recommended === 'N/A') {
      return ['message' => $this->t('Info'), 'color' => 'blue'];
    }

    // Convertir les valeurs en octets pour comparaison
    $current_bytes = $this->parseSize($current);
    $recommended_bytes = $this->parseSize($recommended);

    if ($current_bytes >= $recommended_bytes) {
      return ['message' => $this->t('OK'), 'color' => 'green'];
    } else {
      return ['message' => $this->t('Trop bas'), 'color' => 'red'];
    }
  }

  /**
   * Parse une taille de fichier depuis la configuration PHP.
   */
  protected function parseSize($size) {
    if (is_numeric($size)) {
      return (int) $size;
    }
    
    $size = trim($size);
    $last = strtolower($size[strlen($size) - 1]);
    $size = (int) $size;
    
    switch ($last) {
      case 'g':
        $size *= 1024;
      case 'm':
        $size *= 1024;
      case 'k':
        $size *= 1024;
    }
    
    return $size;
  }

  /**
   * Crée les répertoires manquants.
   */
  public function createDirectories() {
    $directories = [
      'public://import_reglementation_uploads',
      'public://reglementation/pdf',
    ];

    $created = 0;
    foreach ($directories as $dir) {
      if ($this->fileSystem->prepareDirectory($dir, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS)) {
        $created++;
      }
    }

    if ($created > 0) {
      $this->messenger()->addStatus($this->t('@count répertoire(s) créé(s) avec succès.', ['@count' => $created]));
    } else {
      $this->messenger()->addWarning($this->t('Aucun répertoire n\'a pu être créé.'));
    }

    return $this->redirect('import_reglementation.diagnostic');
  }

} 