<?php

namespace <PERSON><PERSON>al\import_reglementation\Form;

use <PERSON><PERSON><PERSON>\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use <PERSON><PERSON><PERSON>\Core\Logger\LoggerChannelFactoryInterface;
use <PERSON>upal\Core\Messenger\MessengerInterface;
use Drupal\Core\TempStore\PrivateTempStoreFactory;
use Drupal\Core\Render\Markup;
use Drupal\file\Entity\File;
use Drupal\import_reglementation\Service\PdfZipProcessor;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;

// Import global functions
use function batch_set;

/**
 * Formulaire pour importer des PDFs depuis un fichier ZIP.
 */
class ImportPdfZipForm extends FormBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * The messenger service.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;

  /**
   * The private temp store.
   *
   * @var \Drupal\Core\TempStore\PrivateTempStoreFactory
   */
  protected $tempStore;

  /**
   * Constructs a new ImportPdfZipForm object.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    FileSystemInterface $file_system,
    LoggerChannelFactoryInterface $logger_factory,
    MessengerInterface $messenger,
    PrivateTempStoreFactory $temp_store
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->fileSystem = $file_system;
    $this->loggerFactory = $logger_factory;
    $this->messenger = $messenger;
    $this->tempStore = $temp_store;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('file_system'),
      $container->get('logger.factory'),
      $container->get('messenger'),
      $container->get('tempstore.private')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'import_pdf_zip_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['description'] = [
      '#markup' => '<p>' . $this->t('Importez des fichiers PDF. Les PDFs seront automatiquement importés vers les nœuds de réglementation correspondants basés sur leurs titres.') . '</p>',
    ];

    // Préparer le répertoire d'upload
    $upload_location = 'public://import_reglementation_uploads';
    $this->fileSystem->prepareDirectory($upload_location, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);

    // Obtenir les limites du serveur
    $max_filesize = $this->getMaxFileSize();
    $max_files = 10; // AUGMENTÉ : Permettre plus de fichiers à la fois

    $form['server_info'] = [
      '#type' => 'details',
      '#title' => $this->t('Informations serveur'),
      '#open' => FALSE,
    ];

    $form['server_info']['limits'] = [
      '#markup' => '<p>' . $this->t('Taille maximale par fichier: @size<br>Nombre maximum de fichiers: @count<br><strong>✅ Fichiers volumineux supportés (jusqu\'à @size)</strong>', [
        '@size' => $this->formatBytes($max_filesize),
        '@count' => $max_files,
      ]) . '</p>',
    ];

    // Champ d'upload de fichiers PDF multiples avec limites adaptées
    $form['pdf_files'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Fichiers PDF à importer'),
      '#description' => $this->t('Sélectionnez jusqu\'à @count fichiers PDF (max @size chacun). <strong>Les fichiers de plus de 2MB sont maintenant supportés !</strong>', [
        '@count' => $max_files,
        '@size' => $this->formatBytes($max_filesize),
      ]),
      '#upload_location' => rtrim($upload_location, '/') . '/',
      '#multiple' => TRUE,
      '#required' => TRUE,
      '#upload_validators' => [
        'FileExtension' => ['extensions' => 'pdf'],
        // Validation de taille gérée par notre hook_file_validate()
      ],
    ];

    $form['options'] = [
      '#type' => 'details',
      '#title' => $this->t('Options avancées'),
      '#open' => FALSE,
    ];

    $form['options']['language'] = [
      '#type' => 'select',
      '#title' => $this->t('Langue forcée'),
      '#description' => $this->t('Forcer une langue spécifique au lieu de la détection automatique.'),
      '#options' => [
        '' => $this->t('- Détection automatique -'),
        'fr' => $this->t('Français'),
        'ar' => $this->t('Arabe'),
      ],
      '#default_value' => '',
    ];

    $form['options']['dry_run'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Mode simulation'),
      '#description' => $this->t('Analyser les fichiers sans effectuer l\'import réel.'),
      '#default_value' => FALSE,
    ];

    $form['options']['overwrite'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Écraser les fichiers existants'),
      '#description' => $this->t('Remplacer les fichiers PDF déjà attachés aux nœuds.'),
      '#default_value' => FALSE,
    ];

    // Ajouter des options pour gérer les gros volumes
    $form['options']['batch_size'] = [
      '#type' => 'select',
      '#title' => $this->t('Taille du lot de traitement'),
      '#description' => $this->t('Nombre de fichiers traités simultanément. Réduisez si vous avez des problèmes de mémoire.'),
      '#options' => [
        '1' => $this->t('1 fichier à la fois (plus lent, plus sûr)'),
        '3' => $this->t('3 fichiers à la fois (recommandé)'),
        '5' => $this->t('5 fichiers à la fois (par défaut)'),
        '10' => $this->t('10 fichiers à la fois (rapide, risqué)'),
      ],
      '#default_value' => '3',
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];

    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Importer les PDFs'),
      '#button_type' => 'primary',
    ];

    return $form;
  }

  /**
   * Formate une taille en octets en format lisible.
   *
   * @param int $bytes
   *   Taille en octets.
   *
   * @return string
   *   Taille formatée.
   */
  protected function formatBytes($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= (1 << (10 * $pow));
    
    return round($bytes, 2) . ' ' . $units[$pow];
  }

  /**
   * Obtient la taille maximale de fichier autorisée.
   *
   * @return int
   *   Taille maximale en octets.
   */
  protected function getMaxFileSize() {
    // Obtenir les limites PHP
    $upload_max = $this->parseSize(ini_get('upload_max_filesize'));
    $post_max = $this->parseSize(ini_get('post_max_size'));
    $memory_limit = $this->parseSize(ini_get('memory_limit'));
    
    // Si memory_limit est -1 (illimité), l'ignorer dans le calcul
    if ($memory_limit <= 0) {
      $max_size = min($upload_max, $post_max);
    } else {
      $max_size = min($upload_max, $post_max, $memory_limit);
    }
    
    // MODIFICATION : Permettre des fichiers plus volumineux
    // Minimum de 10MB même si les limites serveur sont plus basses
    $min_allowed = 10 * 1024 * 1024; // 10MB minimum
    
    // Si les limites serveur permettent plus, utiliser jusqu'à 100MB
    $max_allowed = 100 * 1024 * 1024; // 100MB maximum
    
    // Appliquer une marge de sécurité de 10% seulement (au lieu de 20%)
    $safe_size = $max_size * 0.9;
    
    // Si les limites serveur sont très élevées, utiliser une limite raisonnable
    if ($safe_size > $max_allowed) {
      $final_size = $max_allowed; // Plafonner à 100MB
    } else {
      // Garantir au minimum 10MB
      $final_size = max($min_allowed, $safe_size);
    }
    
    // Log pour debugging
    $this->loggerFactory->get('import_reglementation')->info(
      'Limites de fichier calculées: upload_max=@upload, post_max=@post, memory=@memory, final=@final',
      [
        '@upload' => $this->formatBytes($upload_max),
        '@post' => $this->formatBytes($post_max),
        '@memory' => $memory_limit > 0 ? $this->formatBytes($memory_limit) : 'illimité',
        '@final' => $this->formatBytes($final_size),
      ]
    );
    
    return (int) $final_size;
  }

  /**
   * Parse une taille de fichier depuis la configuration PHP.
   *
   * @param string $size
   *   Taille au format PHP (ex: "8M", "2G").
   *
   * @return int
   *   Taille en octets.
   */
  protected function parseSize($size) {
    $size = trim($size);
    $last = strtolower($size[strlen($size) - 1]);
    $size = (int) $size;
    
    switch ($last) {
      case 'g':
        $size *= 1024;
      case 'm':
        $size *= 1024;
      case 'k':
        $size *= 1024;
    }
    
    return $size;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $language = $form_state->getValue('language');
    $dry_run = $form_state->getValue('dry_run');
    
    $overwrite = $form_state->getValue('overwrite');

    $file_inputs = $form_state->getValue('pdf_files');
    $valid_fids_for_batch = [];
    $problematic_files_info = [];

    if (is_array($file_inputs)) {
      foreach ($file_inputs as $file_input_item) {
        if (is_numeric($file_input_item)) {
          $valid_fids_for_batch[] = (int) $file_input_item;
        }
        elseif ($file_input_item instanceof UploadedFile) {
          $filename = $file_input_item->getClientOriginalName();
          $problematic_files_info[] = $filename;
          $this->loggerFactory->get('import_reglementation')->error('Uploaded file @filename was not processed into a Drupal file entity before batch submission. Skipping this file.', ['@filename' => $filename]);
          $this->messenger->addError($this->t('The file @filename could not be processed correctly and was skipped. It might not have been saved to the server.', ['@filename' => $filename]));
        }
        else {
          $this->loggerFactory->get('import_reglementation')->warning('Unexpected item type in pdf_files form value. Type: @type. Value: @value. Skipping.', [
            '@type' => gettype($file_input_item),
            '@value' => print_r($file_input_item, TRUE),
          ]);
          $this->messenger->addWarning($this->t('An unexpected item was encountered during file processing and was skipped.'));
        }
      }
    }

    if (empty($valid_fids_for_batch) && !empty($file_inputs)) {
      if (!empty($problematic_files_info)) {
        $this->messenger->addError($this->t('No valid files could be prepared for import. Problematic files: @files. Please check the file uploads and try again.', ['@files' => implode(', ', $problematic_files_info)]));
      } else {
        $this->messenger->addError($this->t('No valid files could be prepared for import. Please check the file uploads and try again.'));
      }
      return;
    }
    if (empty($valid_fids_for_batch) && empty($file_inputs)) {
      $this->messenger->addError($this->t('No files were uploaded.'));
      return;
    }

    // Préparer le batch uniquement avec les FIDs valides
    $batch = [
      'title' => $dry_run ? $this->t('Simulation d\'import PDF...') : $this->t('Import des PDFs en cours...'),
      'operations' => [],
      'finished' => [static::class, 'importPdfBatchFinished'],
      'progressive' => TRUE,
    ];

    foreach ($valid_fids_for_batch as $fid) {
      $batch['operations'][] = [
        [static::class, 'processUploadedPdfBatch'],
        [$fid, $language, $dry_run, $overwrite],
      ];
    }

    if (empty($batch['operations'])) {
      if (!empty($problematic_files_info)) {
        $this->messenger->addWarning($this->t('None of the uploaded files could be processed for import. Problematic files: @files', ['@files' => implode(', ', $problematic_files_info)]));
      }
      elseif (empty($file_inputs)) {
        // Message for no files uploaded already shown.
      }
      else {
        $this->messenger->addWarning($this->t('No files to process in the batch.'));
      }
      return; // Don't set an empty batch
    }

    // Use batch_set - this is the standard Drupal way
    batch_set($batch);
  }

  /**
   * Batch operation pour traiter un fichier PDF uploadé.
   */
  public static function processUploadedPdfBatch($fid, $language, $dry_run, $overwrite, &$context) {
    $file = \Drupal\file\Entity\File::load($fid);
    if (!$file) {
      if (!isset($context['results'])) {
        $context['results'] = [
          'total' => 0,
          'success' => 0,
          'errors' => 0,
          'skipped' => 0,
          'details' => [],
        ];
      }
      $context['results']['total']++;
      $context['results']['errors']++;
      $context['results']['details'][] = [
        'filename' => 'Fichier inconnu (fid: ' . $fid . ')',
        'success' => FALSE,
        'skipped' => FALSE,
        'message' => 'Impossible de charger le fichier.',
        'node_id' => null,
        'file_id' => null,
        'detected_language' => '',
        'search_attempts' => [],
      ];
      return;
    }
    $pdf_processor = \Drupal::service('import_reglementation.pdf_zip_processor');
    $result = $pdf_processor->processPdfFromUrl($file->getFileUri(), $language, $dry_run, $overwrite);
    if (!isset($context['results'])) {
      $context['results'] = [
        'total' => 0,
        'success' => 0,
        'errors' => 0,
        'skipped' => 0,
        'details' => [],
      ];
    }
    $context['results']['total']++;
    if ($result['success']) {
      $context['results']['success']++;
    } elseif ($result['skipped']) {
      $context['results']['skipped']++;
    } else {
      $context['results']['errors']++;
    }
    $context['results']['details'][] = $result;
    $context['message'] = t('Traitement: @filename', ['@filename' => $file->getFilename()]);
  }

  /**
   * Batch finished callback.
   */
  public static function importPdfBatchFinished($success, $results, $operations) {
    $messenger = \Drupal::messenger();

    if ($success) {
      $total = $results['total'] ?? 0;
      $success_count = $results['success'] ?? 0;
      $errors = is_array($results['errors'] ?? 0) ? count($results['errors']) : ($results['errors'] ?? 0);
      $error_messages = is_array($results['errors'] ?? null) ? $results['errors'] : [];
      $skipped = $results['skipped'] ?? 0;
      $details = $results['details'] ?? [];

      // Message principal
      $messenger->addStatus(t('Import terminé: @success succès, @errors erreurs, @skipped ignorés sur @total fichiers.',
        [
          '@success' => $success_count,
          '@errors' => $errors,
          '@skipped' => $skipped,
          '@total' => $total,
        ]
      ));

      // Détails des fichiers traités avec succès
      if ($success_count > 0) {
        $success_files = [];
        foreach ($details as $detail) {
          if ($detail['success']) {
            $success_files[] = sprintf('%s → Nœud #%d', 
              basename($detail['filename']), 
              $detail['node_id']
            );
          }
        }
        
        if (!empty($success_files)) {
          $message = t('Fichiers importés avec succès:') . '<br>';
          $message .= '• ' . implode('<br>• ', array_slice($success_files, 0, 10));
          if (count($success_files) > 10) {
            $message .= '<br>• ' . t('... et @count autres', ['@count' => count($success_files) - 10]);
          }
          $messenger->addStatus(\Drupal\Core\Render\Markup::create($message));
        }
      }

      // Détails des fichiers ignorés
      if ($skipped > 0) {
        $skipped_files = [];
        foreach ($details as $detail) {
          if ($detail['skipped']) {
            $skipped_files[] = sprintf('%s → %s', 
              basename($detail['filename']), 
              $detail['message']
            );
          }
        }
        
        if (!empty($skipped_files)) {
          $message = t('Fichiers ignorés:') . '<br>';
          $message .= '• ' . implode('<br>• ', array_slice($skipped_files, 0, 5));
          if (count($skipped_files) > 5) {
            $message .= '<br>• ' . t('... et @count autres', ['@count' => count($skipped_files) - 5]);
          }
          $messenger->addWarning(\Drupal\Core\Render\Markup::create($message));
        }
      }

      // Détails des erreurs
      if ($errors > 0) {
        $error_files = [];
        foreach ($details as $detail) {
          if (!$detail['success'] && !$detail['skipped']) {
            $error_files[] = sprintf('%s → %s', 
              basename($detail['filename']), 
              $detail['message']
            );
          }
        }
        // Afficher aussi les erreurs générales (hors fichiers)
        if (!empty($error_messages)) {
          foreach ($error_messages as $msg) {
            $error_files[] = $msg;
          }
        }
        if (!empty($error_files)) {
          $message = t('Erreurs rencontrées:') . '<br>';
          $message .= '• ' . implode('<br>• ', array_slice($error_files, 0, 5));
          if (count($error_files) > 5) {
            $message .= '<br>• ' . t('... et @count autres', ['@count' => count($error_files) - 5]);
          }
          $messenger->addError(\Drupal\Core\Render\Markup::create($message));
        }
      }
    } else {
      $messenger->addError(t('L\'import a échoué. Consultez les logs pour plus d\'informations.'));
    }
  }

}
