<?php

namespace Dr<PERSON>al\import_reglementation\Form;

use <PERSON><PERSON><PERSON>\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use <PERSON>upal\Core\Url;
use <PERSON><PERSON>al\import_reglementation\Service\CsvImporter;
use Symfony\Component\DependencyInjection\ContainerInterface;

class ImportReglementationForm extends FormBase {

  /**
   * The CSV importer service.
   *
   * @var \Drupal\import_reglementation\Service\CsvImporter
   */
  protected $csvImporter;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * Constructs a new ImportReglementationForm.
   *
   * @param \Drupal\import_reglementation\Service\CsvImporter $csv_importer
   *   The CSV importer service.
   * @param \Drupal\Core\File\FileSystemInterface $file_system
   *   The file system service.
   */
  public function __construct(CsvImporter $csv_importer, FileSystemInterface $file_system) {
    $this->csvImporter = $csv_importer;
    $this->fileSystem = $file_system;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('import_reglementation.csv_importer'),
      $container->get('file_system')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'import_reglementation_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['csv_file'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Fichier CSV'),
      '#description' => $this->t('Sélectionnez le fichier CSV contenant les réglementations à importer.'),
      '#upload_location' => 'public://imports/',
      '#required' => TRUE,
      '#upload_validators' => [
        'FileExtension' => ['extensions' => 'csv'],
      ],
    ];

    $form['delimiter'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Délimiteur'),
      '#default_value' => ',',
      '#required' => TRUE,
      '#size' => 2,
      '#maxlength' => 1,
      '#description' => $this->t('Le caractère utilisé pour séparer les colonnes (généralement "," ou ";").'),
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];

    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Importer'),
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $file = $form_state->getValue('csv_file');
    $delimiter = $form_state->getValue('delimiter');

    if (!empty($file)) {
      $file = \Drupal::entityTypeManager()->getStorage('file')->load($file[0]);
      $uri = $file->getFileUri();
      $file_path = $this->fileSystem->realpath($uri);

      // Importer directement sans utiliser le batch
      $results = $this->csvImporter->import($file_path, $delimiter);

      // Afficher les résultats
      if ($results['success']) {
        $this->messenger()->addStatus($this->t('@created réglementations créées, @updated mises à jour sur @processed lignes traitées.', [
          '@created' => $results['created'],
          '@updated' => $results['updated'],
          '@processed' => $results['processed'],
        ]));

        // Ajouter un avertissement si aucun élément n'a été créé ou mis à jour
        if ($results['created'] == 0 && $results['updated'] == 0) {
          $this->messenger()->addWarning($this->t('Aucun élément n\'a été créé ou mis à jour malgré @processed lignes traitées.', [
            '@processed' => $results['processed'],
          ]));
        }
      }
      else {
        $this->messenger()->addError($this->t('Une erreur est survenue lors de l\'importation.'));
      }

      // Afficher les erreurs éventuelles
      if (!empty($results['errors'])) {
        foreach ($results['errors'] as $error) {
          $this->messenger()->addError($error);
        }
      }

      // Rediriger vers la page actuelle
      $form_state->setRedirect('<current>');
    }
  }
}