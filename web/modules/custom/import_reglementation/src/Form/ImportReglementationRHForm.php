<?php

namespace Drupal\import_reglementation\Form;

use <PERSON>upal\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use Dr<PERSON>al\import_reglementation\Service\CsvImporterRH;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Formulaire d'importation pour les réglementations RH et textes généraux.
 */
class ImportReglementationRHForm extends FormBase {

  /**
   * Le service d'importation CSV RH.
   *
   * @var \Drupal\import_reglementation\Service\CsvImporterRH
   */
  protected $csvImporterRH;

  /**
   * Le service de système de fichiers.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * Constructeur.
   *
   * @param \Drupal\import_reglementation\Service\CsvImporterRH $csv_importer_rh
   *   Le service d'importation CSV RH.
   * @param \Drupal\Core\File\FileSystemInterface $file_system
   *   Le service de système de fichiers.
   */
  public function __construct(CsvImporterRH $csv_importer_rh, FileSystemInterface $file_system) {
    $this->csvImporterRH = $csv_importer_rh;
    $this->fileSystem = $file_system;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('import_reglementation.csv_importer_rh'),
      $container->get('file_system')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'import_reglementation_rh_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['description'] = [
      '#type' => 'markup',
      '#markup' => '<p>' . $this->t('Ce formulaire permet d\'importer des réglementations au format RH et textes généraux depuis un fichier CSV.') . '</p>',
    ];

    $form['csv_file'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Fichier CSV RH'),
      '#description' => $this->t('Sélectionnez le fichier CSV contenant les réglementations RH à importer. Format attendu: Sous secteur, Type, AR, Thème, AR, N° du texte, Intitulé en Arabe, Intitulé en Français, Date De publication, Pièces Jointes'),
      '#upload_location' => 'public://imports/',
      '#required' => TRUE,
      '#upload_validators' => [
        'FileExtension' => ['extensions' => 'csv'],
      ],
    ];

    $form['delimiter'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Délimiteur'),
      '#default_value' => ',',
      '#required' => TRUE,
      '#size' => 2,
      '#maxlength' => 1,
      '#description' => $this->t('Le caractère utilisé pour séparer les colonnes (généralement "," ou ";").'),
    ];

    $form['preview'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Mode aperçu'),
      '#description' => $this->t('Cochez cette case pour valider le fichier sans effectuer l\'importation.'),
      '#default_value' => FALSE,
    ];

    $form['actions'] = [
      '#type' => 'actions',
    ];

    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Importer'),
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    $file = $form_state->getValue('csv_file');
    
    if (empty($file)) {
      $form_state->setErrorByName('csv_file', $this->t('Veuillez sélectionner un fichier CSV.'));
      return;
    }

    // Charger le fichier et vérifier sa structure
    $file_entity = \Drupal::entityTypeManager()->getStorage('file')->load($file[0]);
    if (!$file_entity) {
      $form_state->setErrorByName('csv_file', $this->t('Fichier non trouvé.'));
      return;
    }

    $uri = $file_entity->getFileUri();
    $file_path = $this->fileSystem->realpath($uri);
    $delimiter = $form_state->getValue('delimiter');

    // Valider la structure du fichier
    if (($handle = fopen($file_path, 'r')) !== FALSE) {
      $header = fgetcsv($handle, 0, $delimiter);
      fclose($handle);

      if (!$header) {
        $form_state->setErrorByName('csv_file', $this->t('Impossible de lire l\'en-tête du fichier CSV.'));
        return;
      }

      // Vérifier les colonnes requises
      $required_columns = [
        'Sous secteur', 'Type', 'Thème', 'N° du texte', 
        'Intitulé en Français', 'Date De publication'
      ];
      
      $missing_columns = [];
      foreach ($required_columns as $column) {
        if (!in_array($column, $header)) {
          $missing_columns[] = $column;
        }
      }

      if (!empty($missing_columns)) {
        $form_state->setErrorByName('csv_file', $this->t('Colonnes manquantes dans le fichier CSV: @columns', [
          '@columns' => implode(', ', $missing_columns),
        ]));
      }
    } else {
      $form_state->setErrorByName('csv_file', $this->t('Impossible d\'ouvrir le fichier CSV.'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $file = $form_state->getValue('csv_file');
    $delimiter = $form_state->getValue('delimiter');
    $preview_mode = $form_state->getValue('preview');

    if (!empty($file)) {
      $file_entity = \Drupal::entityTypeManager()->getStorage('file')->load($file[0]);
      $uri = $file_entity->getFileUri();
      $file_path = $this->fileSystem->realpath($uri);

      if ($preview_mode) {
        // Mode aperçu - valider seulement
        $this->previewFile($file_path, $delimiter);
      } else {
        // Importation réelle
        $results = $this->csvImporterRH->import($file_path, $delimiter);

        // Afficher les résultats
        if ($results['success']) {
          $this->messenger()->addStatus($this->t('Importation RH terminée avec succès! @created réglementations créées, @updated mises à jour sur @processed lignes traitées.', [
            '@created' => $results['created'],
            '@updated' => $results['updated'],
            '@processed' => $results['processed'],
          ]));

          if ($results['created'] == 0 && $results['updated'] == 0) {
            $this->messenger()->addWarning($this->t('Aucun élément n\'a été créé ou mis à jour malgré @processed lignes traitées.', [
              '@processed' => $results['processed'],
            ]));
          }
        } else {
          $this->messenger()->addError($this->t('L\'importation RH a échoué.'));
        }

        // Afficher les erreurs s'il y en a
        if (!empty($results['errors'])) {
          foreach ($results['errors'] as $error) {
            $this->messenger()->addError($error);
          }
        }
      }
    }
  }

  /**
   * Affiche un aperçu du fichier CSV.
   *
   * @param string $file_path
   *   Chemin vers le fichier.
   * @param string $delimiter
   *   Délimiteur CSV.
   */
  protected function previewFile($file_path, $delimiter) {
    if (($handle = fopen($file_path, 'r')) !== FALSE) {
      $header = fgetcsv($handle, 0, $delimiter);
      
      $this->messenger()->addStatus($this->t('Aperçu du fichier CSV:'));
      $this->messenger()->addStatus($this->t('Colonnes détectées: @columns', [
        '@columns' => implode(', ', $header),
      ]));

      // Compter les lignes
      $line_count = 0;
      while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
        $line_count++;
      }

      $this->messenger()->addStatus($this->t('Nombre de lignes de données: @count', [
        '@count' => $line_count,
      ]));

      fclose($handle);
    }
  }
}
