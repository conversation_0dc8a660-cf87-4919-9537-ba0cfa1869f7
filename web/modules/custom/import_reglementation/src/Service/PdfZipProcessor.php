<?php

namespace Drupal\import_reglementation\Service;

use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use <PERSON><PERSON>al\Core\Logger\LoggerChannelFactoryInterface;
use <PERSON><PERSON>al\file\Entity\File;
use <PERSON><PERSON><PERSON>\node\Entity\Node;

/**
 * Service pour traiter les fichiers PDF.
 */
class PdfZipProcessor {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * Constructs a new PdfZipProcessor object.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
    FileSystemInterface $file_system,
    LoggerChannelFactoryInterface $logger_factory
  ) {
    $this->entityTypeManager = $entity_type_manager;
    $this->fileSystem = $file_system;
    $this->loggerFactory = $logger_factory;
  }

  /**
   * Génère un nom de fichier sécurisé et plus court.
   *
   * @param string $original_filename
   *   Le nom de fichier original.
   * @param string $extension
   *   L\'extension du fichier.
   *
   * @return string
   *   Le nom de fichier sécurisé.
   */
  protected function generateSafeFilename($original_filename, $extension) {
    // Extraire le numéro de référence si présent
    preg_match('/رقم\s+(\d+(?:\.\d+)+)/', $original_filename, $matches);
    $ref_number = $matches[1] ?? '';
    
    // Extraire la date si présente
    preg_match('/(\d{4})/', $original_filename, $date_matches);
    $year = $date_matches[1] ?? '';
    
    // Créer un nom de fichier plus court
    $base_name = '';
    if (!empty($ref_number)) {
      $base_name = 'reglement_' . $ref_number;
    } else {
      // Utiliser un hash du nom original si pas de numéro de référence
      $base_name = 'reglement_' . substr(md5($original_filename), 0, 8);
    }
    
    // Ajouter l\'année si disponible
    if (!empty($year)) {
      $base_name .= '_' . $year;
    }
    
    // Ajouter l\'extension
    return $base_name . '.' . $extension;
  }

  /**
   * Trouve un nœud de réglementation par son titre exact (méthode publique pour tests).
   *
   * @param string $title
   *   Le titre exact du nœud.
   * @param string $language
   *   La langue du nœud.
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null si non trouvé.
   */
  public function findNodeByTitle($title, $language = '') {
    try {
      // Log de démarrage de la recherche
      $this->loggerFactory->get('import_reglementation')->info(
        'Recherche nœud pour titre: "@title" (langue: @lang)',
        ['@title' => $title, '@lang' => $language ?: 'auto']
      );
      
      // Normaliser le titre pour la recherche
      $normalized_title = $this->normalizeTitle($title);
      
      // 1. PRIORITÉ ABSOLUE : Recherche par numéro de référence dans field_numero_de_text
      $node = $this->searchNodeByReferenceNumber($title, $language);
      if ($node) {
        $this->loggerFactory->get('import_reglementation')->info(
          'Nœud trouvé par numéro de référence → #@nid',
          ['@nid' => $node->id()]
        );
        return $node;
      }
      
      // 2. Recherche exacte dans le titre
      $node = $this->searchNodeByTitle($title, $language);
      if ($node) {
        $this->loggerFactory->get('import_reglementation')->info(
          'Nœud trouvé par recherche exacte → #@nid',
          ['@nid' => $node->id()]
        );
        return $node;
      }
      
      // 3. Si pas trouvé, essayer avec le titre normalisé
      if ($normalized_title !== $title) {
        $node = $this->searchNodeByTitle($normalized_title, $language);
        if ($node) {
          $this->loggerFactory->get('import_reglementation')->info(
            'Nœud trouvé par recherche normalisée → #@nid',
            ['@nid' => $node->id()]
          );
          return $node;
        }
      }
      
      // 4. Recherche LIKE (contient le titre)
      $node = $this->searchNodeByTitleLike($title, $language);
      if ($node) {
        $this->loggerFactory->get('import_reglementation')->info(
          'Nœud trouvé par recherche LIKE → #@nid',
          ['@nid' => $node->id()]
        );
        return $node;
      }
      
      // 5. Recherche par mots-clés
      $node = $this->searchNodeByKeywords($title, $language);
      if ($node) {
        $this->loggerFactory->get('import_reglementation')->info(
          'Nœud trouvé par recherche mots-clés → #@nid',
          ['@nid' => $node->id()]
        );
        return $node;
      }
      
      // 6. Si toujours pas trouvé, recherche approximative (seuil réduit)
      $node = $this->searchNodeByTitleApproximate($title, $language);
      if ($node) {
        $this->loggerFactory->get('import_reglementation')->info(
          'Nœud trouvé par recherche approximative → #@nid',
          ['@nid' => $node->id()]
        );
        return $node;
      }
      
      $this->loggerFactory->get('import_reglementation')->warning(
        'Aucun nœud trouvé pour le titre: "@title"',
        ['@title' => $title]
      );
      return null;
      
    } catch (\Exception $e) {
      $this->loggerFactory->get('import_reglementation')->error(
        'Erreur lors de la recherche du nœud: @message',
        ['@message' => $e->getMessage()]
      );
      return null;
    }
  }

  /**
   * Recherche un nœud par numéro de référence (PRIORITÉ AU CHAMP field_numero_de_text).
   *
   * @param string $title
   *   Le titre contenant les numéros de référence.
   * @param string $language
   *   La langue du nœud.
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null.
   */
  protected function searchNodeByReferenceNumber($title, $language = '') {
    // Extraire les numéros de référence du titre
    $reference_numbers = $this->extractReferenceNumbers($title);
    
    if (empty($reference_numbers)) {
      return null;
    }
    
    $this->loggerFactory->get('import_reglementation')->info(
      'Numéros de référence extraits du titre "@title": @numbers',
      [
        '@title' => $title,
        '@numbers' => implode(', ', $reference_numbers)
      ]
    );
    
    foreach ($reference_numbers as $ref_number) {
      // PRIORITÉ 1 : Chercher dans le champ field_numero_de_text (plus fiable)
      $node = $this->searchNodeByReferenceInField($ref_number, $language);
      if ($node) {
        $this->loggerFactory->get('import_reglementation')->info(
          'Nœud trouvé par numéro "@ref" dans field_numero_de_text → #@nid (@node_title)',
          [
            '@ref' => $ref_number,
            '@nid' => $node->id(),
            '@node_title' => $node->getTitle()
          ]
        );
        return $node;
      }
      
      // PRIORITÉ 2 : Chercher dans le titre des nœuds (moins fiable)
      $node = $this->searchNodeByReferenceInTitle($ref_number, $language);
      if ($node) {
        $this->loggerFactory->get('import_reglementation')->info(
          'Nœud trouvé par numéro "@ref" dans le titre → #@nid (@node_title)',
          [
            '@ref' => $ref_number,
            '@nid' => $node->id(),
            '@node_title' => $node->getTitle()
          ]
        );
        return $node;
      }
    }
    
    return null;
  }

  /**
   * Extrait les numéros de référence d\'un titre (AMÉLIORÉ).
   *
   * @param string $title
   *   Le titre à analyser.
   *
   * @return array
   *   Liste des numéros de référence trouvés.
   */
  protected function extractReferenceNumbers($title) {
    $numbers = [];
    
    // Pattern 1: Numéros avec des points (ex: 31.13, 1.18.15, 2.21.968)
    preg_match_all('/\b\d+(?:\.\d+)+\b/', $title, $matches);
    if (!empty($matches[0])) {
      $numbers = array_merge($numbers, $matches[0]);
    }
    
    // Pattern 2: Numéros avec "رقم" (numéro en arabe)
    preg_match_all('/رقم\s+(\d+(?:\.\d+)+)/', $title, $matches);
    if (!empty($matches[1])) {
      $numbers = array_merge($numbers, $matches[1]);
    }
    
    // Pattern 3: Numéros après "loi", "décret", etc.
    preg_match_all('/(?:loi|décret|arrêté|dahir|ظهير|قانون)\s+(?:n°|رقم|numéro)?\s*(\d+(?:\.\d+)+)/i', $title, $matches);
    if (!empty($matches[1])) {
      $numbers = array_merge($numbers, $matches[1]);
    }
    
    // Pattern 4: Numéros avec tirets (ex: 31-13, 1-18-15)
    preg_match_all('/\b\d+(?:-\d+)+\b/', $title, $matches);
    if (!empty($matches[0])) {
      // Convertir les tirets en points pour la recherche
      foreach ($matches[0] as $match) {
        $numbers[] = str_replace('-', '.', $match);
        $numbers[] = $match; // Garder aussi la version avec tirets
      }
    }
    
    // Pattern 5: Numéros simples précédés de mots-clés (ex: "loi 123", "قانون 456")
    preg_match_all('/(?:loi|décret|arrêté|dahir|ظهير|قانون)\s+(?:n°|رقم|numéro)?\s*(\d+)\b/i', $title, $matches);
    if (!empty($matches[1])) {
      $numbers = array_merge($numbers, $matches[1]);
    }
    
    // Pattern 6: Années (pour les textes datés)
    preg_match_all('/\b(19|20)\d{2}\b/', $title, $matches);
    if (!empty($matches[0])) {
      $numbers = array_merge($numbers, $matches[0]);
    }
    
    // Supprimer les doublons et trier par longueur (les plus longs d\'abord, plus spécifiques)
    $numbers = array_unique($numbers);
    usort($numbers, function($a, $b) {
      // Prioriser les numéros avec points/tirets (plus spécifiques)
      $a_has_separator = strpos($a, '.') !== false || strpos($a, '-') !== false;
      $b_has_separator = strpos($b, '.') !== false || strpos($b, '-') !== false;
      
      if ($a_has_separator && !$b_has_separator) return -1;
      if (!$a_has_separator && $b_has_separator) return 1;
      
      // Sinon, trier par longueur
      return strlen($b) - strlen($a);
    });
    
    return $numbers;
  }

  /**
   * Recherche un nœud par numéro de référence dans le titre.
   *
   * @param string $ref_number
   *   Le numéro de référence à chercher.
   * @param string $language
   *   La langue du nœud.
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null.
   */
  protected function searchNodeByReferenceInTitle($ref_number, $language = '') {
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $ref_number . '%', 'LIKE')
      ->range(0, 5); // Limiter les résultats

    if (!empty($language)) {
      $query->condition('langcode', $language);
    } else {
      // Chercher dans toutes les langues pour les numéros
      // Les numéros sont universels
    }

    $nids = $query->execute();

    if (!empty($nids)) {
      // Prendre le premier résultat, mais vérifier la pertinence
      $nodes = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);
      
      foreach ($nodes as $node) {
        /** @var \Drupal\node\Entity\Node $node */
        // Vérifier que le numéro est bien dans le titre (pas juste une sous-chaîne)
        $node_title = $node->getTitle();
        if (preg_match('/\b' . preg_quote($ref_number, '/') . '\b/', $node_title)) {
          return $node;
        }
      }
    }

    return null;
  }

  /**
   * Recherche un nœud par numéro de référence dans le champ field_numero_de_text (AMÉLIORÉ).
   *
   * @param string $ref_number
   *   Le numéro de référence à chercher.
   * @param string $language
   *   La langue du nœud.
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null.
   */
  protected function searchNodeByReferenceInField($ref_number, $language = '') {
    $this->loggerFactory->get('import_reglementation')->info(
      'Recherche par numéro "@ref" dans field_numero_de_text (langue: @lang)',
      ['@ref' => $ref_number, '@lang' => $language ?: 'toutes']
    );
    
    // Recherche exacte d'abord
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'reglementation')
      ->condition('field_numero_de_text', $ref_number)
      ->range(0, 1);

    if (!empty($language)) {
      $query->condition('langcode', $language);
    }

    $nids = $query->execute();
    if (!empty($nids)) {
      $nid = reset($nids);
      $node = $this->entityTypeManager->getStorage('node')->load($nid);
      $this->loggerFactory->get('import_reglementation')->info(
        'Correspondance exacte trouvée pour "@ref" → Nœud #@nid',
        ['@ref' => $ref_number, '@nid' => $node->id()]
      );
      return $node;
    }

    // Si pas de correspondance exacte, recherche LIKE
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'reglementation')
      ->condition('field_numero_de_text', '%' . $ref_number . '%', 'LIKE')
      ->range(0, 10); // Augmenter la limite pour plus de choix

    if (!empty($language)) {
      $query->condition('langcode', $language);
    }

    $nids = $query->execute();

    if (!empty($nids)) {
      $nodes = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);
      
      // Trier les résultats par pertinence
      $scored_nodes = [];
      
      foreach ($nodes as $node) {
        /** @var \Drupal\node\Entity\Node $node */
        if ($node->hasField('field_numero_de_text') && !$node->get('field_numero_de_text')->isEmpty()) {
          $field_value = $node->get('field_numero_de_text')->value;
          $score = $this->calculateReferenceScore($ref_number, $field_value);
          
          if ($score > 0) {
            $scored_nodes[] = [
              'node' => $node,
              'score' => $score,
              'field_value' => $field_value,
            ];
            
            $this->loggerFactory->get('import_reglementation')->debug(
              'Candidat trouvé: "@ref" vs "@field" → Score: @score (Nœud #@nid)',
              [
                '@ref' => $ref_number,
                '@field' => $field_value,
                '@score' => round($score, 3),
                '@nid' => $node->id(),
              ]
            );
          }
        }
      }
      
      if (!empty($scored_nodes)) {
        // Trier par score décroissant
        usort($scored_nodes, function($a, $b) {
          return $b['score'] <=> $a['score'];
        });
        
        $best_match = $scored_nodes[0];
        
        // Seuil de confiance : accepter si score > 0.7
        if ($best_match['score'] > 0.7) {
          $this->loggerFactory->get('import_reglementation')->info(
            'Meilleure correspondance pour "@ref": "@field" (Score: @score) → Nœud #@nid',
            [
              '@ref' => $ref_number,
              '@field' => $best_match['field_value'],
              '@score' => round($best_match['score'], 3),
              '@nid' => $best_match['node']->id(),
            ]
          );
          return $best_match['node'];
        } else {
          $this->loggerFactory->get('import_reglementation')->warning(
            'Correspondance trouvée mais score trop faible (@score < 0.7) pour "@ref"',
            ['@ref' => $ref_number, '@score' => round($best_match['score'], 3)]
          );
        }
      }
    }

    // Si toujours pas trouvé et pas de langue spécifiée, essayer dans toutes les langues
    if (empty($language)) {
      $this->loggerFactory->get('import_reglementation')->info(
        'Recherche dans toutes les langues pour "@ref"',
        ['@ref' => $ref_number]
      );
      
      foreach (['ar', 'fr'] as $lang) {
        $node = $this->searchNodeByReferenceInField($ref_number, $lang);
        if ($node) {
          return $node;
        }
      }
    }

    return null;
  }

  /**
   * Calcule un score de correspondance entre un numéro de référence et une valeur de champ.
   *
   * @param string $ref_number
   *   Le numéro de référence recherché.
   * @param string $field_value
   *   La valeur du champ field_numero_de_text.
   *
   * @return float
   *   Score de correspondance (0.0 à 1.0).
   */
  protected function calculateReferenceScore($ref_number, $field_value) {
    // Normaliser les valeurs
    $ref_normalized = $this->normalizeReferenceNumber($ref_number);
    $field_normalized = $this->normalizeReferenceNumber($field_value);
    
    // Score 1.0 pour correspondance exacte
    if ($ref_normalized === $field_normalized) {
      return 1.0;
    }
    
    // Score élevé si le numéro de référence est contenu dans le champ
    if (strpos($field_normalized, $ref_normalized) !== false) {
      // Plus le numéro est long, plus le score est élevé
      $length_factor = min(strlen($ref_normalized) / 10, 1.0);
      return 0.9 * $length_factor;
    }
    
    // Score élevé si le champ est contenu dans le numéro de référence
    if (strpos($ref_normalized, $field_normalized) !== false) {
      $length_factor = min(strlen($field_normalized) / 10, 1.0);
      return 0.8 * $length_factor;
    }
    
    // Vérifier les correspondances partielles (ex: 31.13 vs 31-13)
    $ref_parts = preg_split('/[.\-\/]/', $ref_normalized);
    $field_parts = preg_split('/[.\-\/]/', $field_normalized);
    
    if (count($ref_parts) > 1 && count($field_parts) > 1) {
      $common_parts = array_intersect($ref_parts, $field_parts);
      if (!empty($common_parts)) {
        $score = count($common_parts) / max(count($ref_parts), count($field_parts));
        return $score * 0.7; // Score modéré pour correspondance partielle
      }
    }
    
    // Score basé sur la similarité textuelle
    similar_text($ref_normalized, $field_normalized, $percent);
    return ($percent / 100) * 0.5; // Score faible pour similarité générale
  }

  /**
   * Normalise un numéro de référence pour la comparaison.
   *
   * @param string $number
   *   Le numéro à normaliser.
   *
   * @return string
   *   Le numéro normalisé.
   */
  protected function normalizeReferenceNumber($number) {
    // Supprimer les espaces
    $number = trim($number);
    
    // Convertir en minuscules
    $number = strtolower($number);
    
    // Supprimer les caractères non-alphanumériques sauf points, tirets et slashes
    $number = preg_replace('/[^\w.\-\/]/', '', $number);
    
    // Normaliser les séparateurs (convertir tirets et slashes en points)
    $number = str_replace(['-', '/'], '.', $number);
    
    // Supprimer les points multiples
    $number = preg_replace('/\.+/', '.', $number);
    
    // Supprimer les points en début/fin
    $number = trim($number, '.');
    
    return $number;
  }

  /**
   * Recherche un nœud par titre exact.
   *
   * @param string $title
   *   Le titre à rechercher.
   * @param string $language
   *   La langue du nœud.
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null.
   */
  protected function searchNodeByTitle($title, $language = '') {
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'reglementation')
      ->condition('title', $title)
      ->range(0, 1);

    if (!empty($language)) {
      $query->condition('langcode', $language);
    }

    $nids = $query->execute();

    if (!empty($nids)) {
      $nid = reset($nids);
      return $this->entityTypeManager->getStorage('node')->load($nid);
    }

    // Si aucun nœud trouvé et pas de langue spécifiée, privilégier la langue détectée
    if (empty($language)) {
      $detected_lang = $this->detectLanguage($title);
      
      // Essayer d\'abord avec la langue détectée
      $query = $this->entityTypeManager->getStorage('node')->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation')
        ->condition('title', $title)
        ->condition('langcode', $detected_lang)
        ->range(0, 1);

      $nids = $query->execute();

      if (!empty($nids)) {
        $nid = reset($nids);
        $found_node = $this->entityTypeManager->getStorage('node')->load($nid);
        
        // Log pour debugging
        $this->loggerFactory->get('import_reglementation')->info(
          'Nœud trouvé avec langue détectée "@detected" pour titre "@title" → Nœud #@nid (@node_lang)',
          [
            '@detected' => $detected_lang,
            '@title' => $title,
            '@nid' => $found_node->id(),
            '@node_lang' => $found_node->language()->getId(),
          ]
        );
        
        return $found_node;
      }
      
      // Si pas trouvé avec la langue détectée, essayer l\'autre langue
      $fallback_lang = $detected_lang === 'ar' ? 'fr' : 'ar';
      $query = $this->entityTypeManager->getStorage('node')->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation')
        ->condition('title', $title)
        ->condition('langcode', $fallback_lang)
        ->range(0, 1);

      $nids = $query->execute();

      if (!empty($nids)) {
        $nid = reset($nids);
        $found_node = $this->entityTypeManager->getStorage('node')->load($nid);
        
        // Log d\'avertissement car la langue ne correspond pas
        $this->loggerFactory->get('import_reglementation')->warning(
          'Nœud trouvé avec langue différente pour titre "@title": détectée=@detected, trouvée=@found → Nœud #@nid',
          [
            '@title' => $title,
            '@detected' => $detected_lang,
            '@found' => $found_node->language()->getId(),
            '@nid' => $found_node->id(),
          ]
        );
        
        return $found_node;
      }
    }

    return null;
  }

  /**
   * Recherche un nœud par titre contenant une partie du titre (LIKE).
   *
   * @param string $title
   *   Le titre à rechercher.
   * @param string $language
   *   La langue du nœud.
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null.
   */
  protected function searchNodeByTitleLike($title, $language = '') {
    // Normaliser le titre pour la recherche
    $normalized_title = $this->normalizeTitle($title);
    
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'reglementation')
      ->condition('title', '%' . $normalized_title . '%', 'LIKE')
      ->range(0, 10); // Limiter à 10 résultats

    if (!empty($language)) {
      $query->condition('langcode', $language);
    } else {
      // Privilégier la langue détectée
      $detected_lang = $this->detectLanguage($title);
      $query->condition('langcode', $detected_lang);
    }

    $nids = $query->execute();

    if (!empty($nids)) {
      // Prendre le premier résultat le plus proche
      $nodes = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);
      $best_match = null;
      $best_score = 0;
      
      foreach ($nodes as $node) {
        /** @var \Drupal\node\Entity\Node $node */
        $score = $this->calculateTitleSimilarity($title, $node->getTitle());
        if ($score > $best_score) {
          $best_score = $score;
          $best_match = $node;
        }
      }
      
      // Seuil plus bas pour LIKE (40%)
      if ($best_score > 0.4) {
        return $best_match;
      }
    }

    return null;
  }

  /**
   * Recherche un nœud par mots-clés du titre.
   *
   * @param string $title
   *   Le titre à rechercher.
   * @param string $language
   *   La langue du nœud.
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null.
   */
  protected function searchNodeByKeywords($title, $language = '') {
    // Extraire les mots significatifs (au moins 3 caractères)
    $words = $this->extractKeywords($title);
    
    if (empty($words)) {
      return null;
    }
    
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'reglementation');

    if (!empty($language)) {
      $query->condition('langcode', $language);
    } else {
      $detected_lang = $this->detectLanguage($title);
      $query->condition('langcode', $detected_lang);
    }

    // Chercher les nœuds contenant au moins un des mots-clés
    $or_group = $query->orConditionGroup();
    foreach ($words as $word) {
      $or_group->condition('title', '%' . $word . '%', 'LIKE');
    }
    $query->condition($or_group);

    $nids = $query->execute();

    if (!empty($nids)) {
      $nodes = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);
      $best_match = null;
      $best_score = 0;
      
      foreach ($nodes as $node) {
        /** @var \Drupal\node\Entity\Node $node */
        $score = $this->calculateKeywordSimilarity($title, $node->getTitle());
        if ($score > $best_score) {
          $best_score = $score;
          $best_match = $node;
        }
      }
      
      // Seuil pour mots-clés (30%)
      if ($best_score > 0.3) {
        return $best_match;
      }
    }

    return null;
  }

  /**
   * Recherche approximative d\'un nœud par titre.
   *
   * @param string $title
   *   Le titre à rechercher.
   * @param string $language
   *   La langue du nœud.
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud trouvé ou null.
   */
  protected function searchNodeByTitleApproximate($title, $language = '') {
    $query = $this->entityTypeManager->getStorage('node')->getQuery()
      ->accessCheck(FALSE)
      ->condition('type', 'reglementation');

    if (!empty($language)) {
      $query->condition('langcode', $language);
    } else {
      // Privilégier l\'arabe pour les titres arabes
      $detected_lang = $this->detectLanguage($title);
      $query->condition('langcode', $detected_lang);
    }

    $nids = $query->execute();

    if (empty($nids)) {
      return null;
    }

    // Charger tous les nœuds et comparer les titres
    $nodes = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);
    $best_match = null;
    $best_score = 0;

    foreach ($nodes as $node) {
      /** @var \Drupal\node\Entity\Node $node */
      $node_title = $node->getTitle();
      $score = $this->calculateTitleSimilarity($title, $node_title);
      
      // Seuil réduit de 80% à 60% pour plus de flexibilité
      if ($score > $best_score && $score > 0.6) {
        $best_score = $score;
        $best_match = $node;
        
        // Log des correspondances trouvées
        $this->loggerFactory->get('import_reglementation')->info(
          'Correspondance approximative (@score%): "@pdf_title" ≈ "@node_title" → Nœud #@nid',
          [
            '@score' => round($score * 100, 1),
            '@pdf_title' => $title,
            '@node_title' => $node_title,
            '@nid' => $node->id(),
          ]
        );
      }
    }

    return $best_match;
  }

  /**
   * Normalise un titre pour la recherche.
   *
   * @param string $title
   *   Le titre à normaliser.
   *
   * @return string
   *   Le titre normalisé.
   */
  protected function normalizeTitle($title) {
    // Supprimer les espaces en début/fin
    $title = trim($title);
    
    // Normaliser les espaces multiples
    $title = preg_replace('/\s+/', ' ', $title);
    
    // Pour l\'arabe, normaliser les caractères similaires
    if ($this->detectLanguage($title) === 'ar') {
      // Normaliser les hamza - plus de variantes
      $title = str_replace(['أ', 'إ', 'آ', 'ٱ'], 'ا', $title);
      
      // Normaliser les ya et alif maqsura
      $title = str_replace(['ي', 'ى', 'ئ'], 'ي', $title);
      
      // Normaliser les ta marbouta et ha
      $title = str_replace(['ة', 'ه'], 'ه', $title);
      
      // Normaliser les lam-alif
      $title = str_replace('لا', 'لا', $title);
      
      // Supprimer les diacritiques arabes
      $title = preg_replace('/[\x{064B}-\x{065F}\x{0670}\x{06D6}-\x{06ED}]/u', '', $title);
      
      // Normaliser les chiffres arabes vers occidentaux
      $arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', 'و', '٩'];
      $western_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
      $title = str_replace($arabic_digits, $western_digits, $title);
    }
    
    // Normaliser la ponctuation
    $title = str_replace(['؟', '،', '؛'], ['?', ',', ';'], $title);
    
    return $title;
  }

  /**
   * Calcule la similarité entre deux titres.
   *
   * @param string $title1
   *   Premier titre.
   * @param string $title2
   *   Deuxième titre.
   *
   * @return float
   *   Score de similarité (0.0 à 1.0).
   */
  protected function calculateTitleSimilarity($title1, $title2) {
    // Normaliser les deux titres
    $title1 = $this->normalizeTitle($title1);
    $title2 = $this->normalizeTitle($title2);
    
    // Calculer la similarité avec similar_text
    similar_text($title1, $title2, $percent);
    
    return $percent / 100;
  }

  /**
   * Détecte la langue d\'un titre basé sur les caractères utilisés.
   *
   * @param string $title
   *   Le titre à analyser.
   *
   * @return string
   *   Le code de langue détecté ('ar' pour arabe, 'fr' pour français).
   */
  public function detectLanguage($title) {
    // Enlever les espaces et caractères de ponctuation pour le calcul
    $clean_title = preg_replace('/[\s\p{P}]/u', '', $title);
    
    if (empty($clean_title)) {
      return 'fr'; // Par défaut français si vide
    }
    
    // Compter les caractères arabes (plages étendues)
    $arabic_patterns = [
      '[\x{0600}-\x{06FF}]', // Arabic
      '[\x{0750}-\x{077F}]', // Arabic Supplement
      '[\x{08A0}-\x{08FF}]', // Arabic Extended-A
      '[\x{FB50}-\x{FDFF}]', // Arabic Presentation Forms-A
      '[\x{FE70}-\x{FEFF}]', // Arabic Presentation Forms-B
    ];
    
    $arabic_chars = 0;
    foreach ($arabic_patterns as $pattern) {
      $arabic_chars += preg_match_all('/' . $pattern . '/u', $clean_title);
    }
    
    // Compter les caractères latins
    $latin_chars = preg_match_all('/[a-zA-ZÀ-ÿ]/u', $clean_title);
    
    // Compter les chiffres (neutres)
    $numeric_chars = preg_match_all('/[0-9]/u', $clean_title);
    
    // Calculer les pourcentages
    $total_chars = mb_strlen($clean_title, 'UTF-8');
    $arabic_percent = $total_chars > 0 ? ($arabic_chars / $total_chars) * 100 : 0;
    $latin_percent = $total_chars > 0 ? ($latin_chars / $total_chars) * 100 : 0;
    
    // Log pour debugging
    $this->loggerFactory->get('import_reglementation')->debug(
      'Détection langue pour "@title": Arabe=@arabic_chars (@arabic_percent%), Latin=@latin_chars (@latin_percent%), Total=@total',
      [
        '@title' => $title,
        '@arabic_chars' => $arabic_chars,
        '@arabic_percent' => round($arabic_percent, 1),
        '@latin_chars' => $latin_chars,
        '@latin_percent' => round($latin_percent, 1),
        '@total' => $total_chars,
      ]
    );
    
    // Si plus de 30% de caractères arabes, c'est de l'arabe
    if ($arabic_percent > 30) {
      return 'ar';
    }
    
    // Si plus de caractères arabes que latins et au moins 1 caractère arabe, c'est de l'arabe
    if ($arabic_chars > 0 && $arabic_chars >= $latin_chars) {
      return 'ar';
    }
    
    return 'fr';
  }

  /**
   * Génère des variantes d\'un titre pour la recherche.
   *
   * @param string $title
   *   Le titre original.
   *
   * @return array
   *   Liste des variantes du titre.
   */
  protected function generateTitleVariants($title) {
    $variants = [];
    
    // Titre normalisé
    $normalized = $this->normalizeTitle($title);
    if ($normalized !== $title) {
      $variants[] = $normalized;
    }
    
    // Sans numéros à la fin
    $without_numbers = preg_replace('/\s*\d+\s*$/', '', $title);
    if ($without_numbers !== $title) {
      $variants[] = $without_numbers;
    }
    
    // Remplacer underscores et tirets par espaces
    $with_spaces = str_replace(['_', '-'], ' ', $title);
    if ($with_spaces !== $title) {
      $variants[] = $with_spaces;
    }
    
    // Première lettre en majuscule
    $variants[] = ucfirst(strtolower($title));
    
    // Tout en minuscules
    $variants[] = strtolower($title);
    
    // Supprimer les doublons
    return array_unique($variants);
  }

  /**
   * Trouve des nœuds avec des titres similaires.
   *
   * @param string $title
   *   Le titre à rechercher.
   * @param string $language
   *   La langue des nœuds.
   * @param int $limit
   *   Nombre maximum de suggestions.
   *
   * @return array
   *   Liste des titres de nœuds similaires.
   */
  protected function findSimilarNodes($title, $language = '', $limit = 5) {
    try {
      $query = $this->entityTypeManager->getStorage('node')->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation');

      if (!empty($language)) {
        $query->condition('langcode', $language);
      }

      $nids = $query->execute();

      if (empty($nids)) {
        return [];
      }

      $nodes = $this->entityTypeManager->getStorage('node')->loadMultiple($nids);
      $similarities = [];

      foreach ($nodes as $node) {
        /** @var \Drupal\node\Entity\Node $node */
        $node_title = $node->getTitle();
        $similarity = $this->calculateTitleSimilarity($title, $node_title);
        
        if ($similarity > 0.5) { // Seuil de 50% pour les suggestions
          $similarities[$node_title] = $similarity;
        }
      }

      // Trier par similarité décroissante
      arsort($similarities);

      return array_slice(array_keys($similarities), 0, $limit);
      
    } catch (\Exception $e) {
      return [];
    }
  }

  /**
   * Extrait les mots-clés significatifs d\'un titre.
   *
   * @param string $title
   *   Le titre à analyser.
   *
   * @return array
   *   Liste des mots-clés.
   */
  protected function extractKeywords($title) {
    // Normaliser le titre
    $title = $this->normalizeTitle($title);
    
    // Diviser en mots
    $words = preg_split('/\s+/', $title);
    
    $keywords = [];
    foreach ($words as $word) {
      $word = trim($word);
      
      // Ignorer les mots trop courts ou les mots de liaison courants
      if (mb_strlen($word, 'UTF-8') >= 3 && !$this->isStopWord($word)) {
        $keywords[] = $word;
      }
    }
    
    return array_unique($keywords);
  }

  /**
   * Vérifie si un mot est un mot de liaison (stop word).
   *
   * @param string $word
   *   Le mot à vérifier.
   *
   * @return bool
   *   TRUE si c'est un mot de liaison.
   */
  protected function isStopWord($word) {
    $stop_words_ar = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'أو', 'لا', 'قد', 'كان', 'هذا', 'هذه', 'ذلك', 'تلك'];
    $stop_words_fr = ['le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'par', 'pour', 'sur', 'avec', 'dans'];
    
    $word_lower = mb_strtolower($word, 'UTF-8');
    
    return in_array($word_lower, $stop_words_ar) || in_array($word_lower, $stop_words_fr);
  }

  /**
   * Calcule la similarité basée sur les mots-clés communs.
   *
   * @param string $title1
   *   Premier titre.
   * @param string $title2
   *   Deuxième titre.
   *
   * @return float
   *   Score de similarité (0.0 à 1.0).
   */
  protected function calculateKeywordSimilarity($title1, $title2) {
    $keywords1 = $this->extractKeywords($title1);
    $keywords2 = $this->extractKeywords($title2);
    
    if (empty($keywords1) || empty($keywords2)) {
      return 0.0;
    }
    
    $common_keywords = array_intersect($keywords1, $keywords2);
    $total_keywords = array_unique(array_merge($keywords1, $keywords2));
    
    return count($common_keywords) / count($total_keywords);
  }

  /**
   * Traite un fichier PDF depuis une URL (ou un URI de fichier local).
   *
   * @param string $url
   *   URL du fichier PDF ou URI de fichier Drupal (public://, private://, temporary://).
   * @param string $language
   *   Langue forcée (optionnel).
   * @param bool $dry_run
   *   Mode simulation.
   * @param bool $overwrite
   *   Écraser les fichiers existants.
   *
   * @return array
   *   Résultat du traitement.
   */
  public function processPdfFromUrl($url, $language = '', $dry_run = FALSE, $overwrite = FALSE) {
    // Décoder le nom de fichier à partir de l\'URL au cas où il serait encodé
    $decoded_filename = rawurldecode(basename($url));

    $result = [
      'filename' => $decoded_filename, // Utiliser le nom de fichier décodé
      'success' => FALSE,
      'skipped' => FALSE,
      'message' => '',
      'node_id' => null,
      'file_id' => null,
      'detected_language' => '',
      'search_attempts' => [],
    ];

    try {
      // Extraire le titre du nom de fichier DÉCODÉ
      $title = preg_replace('/\.(pdf|PDF)$/', '', $decoded_filename);
      $result['original_title'] = $title;

      // Détecter la langue si pas spécifiée
      $detected_language = empty($language) ? $this->detectLanguage($title) : $language;
      $result['detected_language'] = $detected_language;

      // Log de démarrage du traitement
      $this->loggerFactory->get('import_reglementation')->info(
        'Traitement PDF depuis URL/URI: @uri (titre: "@title", langue forcée: @forced, langue détectée: @detected)',
        [
          '@uri' => $url,
          '@title' => $title,
          '@forced' => empty($language) ? 'non' : $language,
          '@detected' => $detected_language,
        ]
      );

      // Chercher le nœud par titre
      $node = $this->findNodeByTitle($title, $detected_language);

      if (!$node) {
        // Essayer différentes variantes du titre
        $title_variants = $this->generateTitleVariants($title);
        $result['search_attempts'][] = "Titre original: '$title'";
        
        foreach ($title_variants as $variant) {
          $result['search_attempts'][] = "Essai avec: '$variant'";
          $node = $this->findNodeByTitle($variant, $detected_language);
          if ($node) {
            break;
          }
        }
        
        if (!$node) {
          // Recherche dans toutes les langues
          $result['search_attempts'][] = "Recherche dans toutes les langues";
          $node = $this->findNodeByTitle($title, '');
          
          if (!$node) {
            $result['message'] = sprintf(
              "Aucun nœud trouvé pour '%s' (langue: %s). Tentatives: %s",
              $title,
              $detected_language,
              implode('; ', $result['search_attempts'])
            );
            
            // Suggestions de nœuds similaires
            $suggestions = $this->findSimilarNodes($title, $detected_language);
            if (!empty($suggestions)) {
              $result['message'] .= sprintf(
                ". Nœuds similaires trouvés: %s",
                implode(', ', array_slice($suggestions, 0, 3))
              );
            }
            
            return $result;
          }
        }
      }

      $result['node_id'] = $node->id();
      $result['node_title'] = $node->getTitle();
      $result['node_language'] = $node->language()->getId();

      // Vérifier si le nœud a déjà un fichier
      $existing_files = $node->get('field_lien_telechargement')->getValue();
      if (!$overwrite && !empty($existing_files)) {
        $result['skipped'] = TRUE;
        $result['message'] = sprintf(
          "Nœud #%d ('%s') a déjà %d fichier(s) attaché(s)",
          $node->id(),
          $node->getTitle(),
          count($existing_files)
        );
        return $result;
      }

      if ($dry_run) {
        $result['success'] = TRUE;
        $result['message'] = sprintf(
          "SIMULATION: Fichier serait importé vers nœud #%d ('%s')",
          $node->id(),
          $node->getTitle()
        );
        return $result;
      }

      // Lire le contenu du fichier local (car il a été uploadé via le formulaire)
      $pdf_content = null;
      if (preg_match('/^(public|private|temporary):\/\//', $url)) {
        $this->loggerFactory->get('import_reglementation')->info('Lecture du fichier local depuis l\'URI: @uri', ['@uri' => $url]);
        $realpath = $this->fileSystem->realpath($url);
        if ($realpath && file_exists($realpath)) {
          $pdf_content = file_get_contents($realpath);
          if ($pdf_content === false) {
            throw new \Exception(sprintf('Impossible de lire le contenu du fichier local: %s', $url));
          }
        } else {
          throw new \Exception(sprintf('Le fichier local n\'existe pas ou n\'est pas accessible à l\'URI: %s (realpath: %s)', $url, $realpath ?: 'non-convertible'));
        }
      } else {
         // Ce cas ne devrait pas être atteint si le formulaire fournit un URI de fichier Drupal
        throw new \Exception(sprintf('URL de fichier inattendue: %s. Attendu un URI de fichier Drupal.', $url));
      }

      // Créer le répertoire de destination
      $destination_dir = 'public://reglementation/pdf';
      $this->fileSystem->prepareDirectory(
        $destination_dir,
        FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS
      );

      // Générer un nom de fichier sécurisé
      $safe_filename = $this->generateSafeFilename(basename($url), 'pdf');
      $destination = $destination_dir . '/' . $safe_filename;

      // Sauvegarder le fichier
      $file_uri = $this->fileSystem->saveData($pdf_content, $destination, FileSystemInterface::EXISTS_REPLACE);

      if (!$file_uri) {
        throw new \Exception("Impossible de sauvegarder le fichier PDF");
      }

      // Créer l\'entité File
      $file = File::create([
        'filename' => $safe_filename,
        'uri' => $file_uri,
        'status' => 1,
        'uid' => 1, // Ou \Drupal::currentUser()->id(),
      ]);
      $file->save();

      $result['file_id'] = $file->id();

      // Obtenir toutes les traductions du nœud
      $translations = $node->getTranslationLanguages();
      $imported_count = 0;
      $errors = [];

      // Vérifier si le nœud a des traductions
      if (empty($translations)) {
        $this->loggerFactory->get('import_reglementation')->warning(
          'Le nœud #@nid n\'a pas de traductions. Attachement au nœud original.',
          ['@nid' => $node->id()]
        );
        
        // Attacher le fichier au nœud original
        $node->set('field_lien_telechargement', [
          'target_id' => $file->id(),
          'description' => $safe_filename,
        ]);
        $node->save();
        $imported_count++;
      } else {
        // Attacher le fichier à toutes les traductions du nœud
        foreach ($translations as $langcode => $language_object) { // $language is an object
          try {
            $translated_node = $node->getTranslation($langcode);
            
            // Vérifier si le nœud a déjà un fichier (si overwrite n'est pas coché)
            // Cette vérification est déjà faite plus haut pour le nœud principal,
            // mais on pourrait vouloir une logique par traduction si overwrite=FALSE.
            // Pour l'instant, le overwrite global s'applique.
            // Si on veut un contrôle plus fin:
            // if (!$overwrite) {
            //   $existing_translation_files = $translated_node->get('field_lien_telechargement')->getValue();
            //   if (!empty($existing_translation_files)) {
            //     $this->loggerFactory->get('import_reglementation')->info(
            //       'Traduction @lang du Nœud #@nid a déjà un fichier attaché, ignoré.',
            //       ['@lang' => $langcode, '@nid' => $translated_node->id()]
            //     );
            //     continue; // Skip this translation
            //   }
            // }


            // Attacher le fichier à cette traduction
            $translated_node->set('field_lien_telechargement', [
              'target_id' => $file->id(),
              'description' => $safe_filename,
            ]);

            $translated_node->save();
            $imported_count++;

            $this->loggerFactory->get('import_reglementation')->info(
              'PDF importé pour la traduction @lang du nœud #@nid',
              ['@lang' => $langcode, '@nid' => $translated_node->id()]
            );
          } catch (\Exception $e) {
            $errors[] = sprintf("Erreur pour la traduction %s: %s", $langcode, $e->getMessage());
            $this->loggerFactory->get('import_reglementation')->error(
              'Erreur lors de l\'import pour la traduction @lang du nœud #@nid: @message',
              ['@lang' => $langcode, '@nid' => $translated_node->id(), '@message' => $e->getMessage()]
            );
          }
        }
      }

      if ($imported_count > 0) {
        $result['success'] = TRUE;
        $result['message'] = sprintf(
          "Fichier PDF importé avec succès dans %d traduction(s) du nœud #%d",
          $imported_count,
          $node->id()
        );
        if (!empty($errors)) {
          $result['message'] .= " (avec " . count($errors) . " erreur(s) sur certaines traductions)";
        }
      } else {
        if (!empty($errors)){
             $result['message'] = "Le fichier PDF n'a pu être attaché à aucune traduction en raison d'erreurs: " . implode("; ", $errors);
        } else {
            $result['message'] = "Aucune traduction n'a pu être mise à jour avec le fichier PDF (par exemple, si 'overwrite' était désactivé et que toutes les traductions avaient déjà un fichier).";
        }
      }

    } catch (\Exception $e) {
      $result['message'] = "Erreur lors de l'import: " . $e->getMessage();
      $this->loggerFactory->get('import_reglementation')->error(
        'Erreur lors de l\'import du PDF @filename: @message',
        ['@filename' => $decoded_filename, '@message' => $e->getMessage()]
      );
    }

    return $result;
  }

}
