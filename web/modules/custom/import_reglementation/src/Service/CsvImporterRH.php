<?php

namespace Drupal\import_reglementation\Service;

use <PERSON>upal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\Core\StringTranslation\StringTranslationTrait;
use Drupal\node\Entity\Node;
use Drupal\Core\Datetime\DrupalDateTime;

/**
 * Service d'importation CSV spécialement adapté pour le format RH et textes généraux.
 */
class CsvImporterRH {

  use StringTranslationTrait;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * Cache pour les termes de taxonomie.
   *
   * @var array
   */
  protected $termCache = [];

  /**
   * Constructeur.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   Le gestionnaire de types d'entités.
   * @param \Drupal\Core\Logger\LoggerChannelFactoryInterface $logger_factory
   *   La factory de logger.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager, LoggerChannelFactoryInterface $logger_factory) {
    $this->entityTypeManager = $entity_type_manager;
    $this->loggerFactory = $logger_factory;
  }

  /**
   * Importe les données depuis un fichier CSV au format RH.
   *
   * @param string $file_path
   *   Chemin vers le fichier CSV.
   * @param string $delimiter
   *   Délimiteur CSV (par défaut: ',').
   *
   * @return array
   *   Résultats de l'importation.
   */
  public function import($file_path, $delimiter = ',') {
    $results = [
      'success' => FALSE,
      'created' => 0,
      'updated' => 0,
      'processed' => 0,
      'errors' => [],
    ];

    $logger = $this->loggerFactory->get('import_reglementation');
    $logger->notice('Début de l\'importation RH depuis: @file', ['@file' => $file_path]);

    if (!file_exists($file_path)) {
      $results['errors'][] = $this->t('Le fichier @file n\'existe pas.', ['@file' => $file_path]);
      return $results;
    }

    if (($handle = fopen($file_path, 'r')) !== FALSE) {
      // Lire l'en-tête
      $header = fgetcsv($handle, 0, $delimiter);

      if ($header) {
        $header = array_map([$this, 'sanitizeInput'], $header);
      }

      // Vérifier les colonnes requises pour le format RH
      $required_columns = [
        'Sous secteur', 'Type', 'Thème', 'N° du texte',
        'Intitulé en Français', 'Date De publication'
      ];

      $missing_columns = [];
      foreach ($required_columns as $column) {
        if (!in_array($column, $header)) {
          $missing_columns[] = $column;
        }
      }

      if (!empty($missing_columns)) {
        foreach ($missing_columns as $column) {
          $results['errors'][] = $this->t('La colonne requise @column est manquante.', ['@column' => $column]);
        }
        fclose($handle);
        return $results;
      }

      // Traiter chaque ligne
      $line_number = 1;
      $max_lines = 10000;

      while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE && $line_number < $max_lines) {
        $line_number++;
        $results['processed']++;

        // Nettoyer les données
        $data = array_map([$this, 'sanitizeInput'], $data);

        // Ajuster le nombre de colonnes si nécessaire
        if (count($data) !== count($header)) {
          if (count($data) < count($header)) {
            $data = array_pad($data, count($header), '');
          } else {
            $data = array_slice($data, 0, count($header));
          }
        }

        $row = array_combine($header, $data);

        // Vérifier les champs obligatoires
        if (empty($row['Intitulé en Français']) || empty($row['N° du texte'])) {
          $logger->warning('Ligne @line: données obligatoires manquantes', ['@line' => $line_number]);
          $results['errors'][] = $this->t('Ligne @line: titre français ou numéro de texte manquant', ['@line' => $line_number]);
          continue;
        }

        try {
          // Vérifier si le nœud existe déjà
          $query = \Drupal::entityQuery('node')
            ->accessCheck(FALSE)
            ->condition('type', 'reglementation')
            ->condition('field_numero_de_text', $row['N° du texte']);

          $nids = $query->execute();

          if (!empty($nids)) {
            $node = Node::load(reset($nids));
            $results['updated']++;
            $logger->notice('Mise à jour du nœud existant: @numero', ['@numero' => $row['N° du texte']]);
          } else {
            $node = Node::create(['type' => 'reglementation']);
            $results['created']++;
            $logger->notice('Création d\'un nouveau nœud: @numero', ['@numero' => $row['N° du texte']]);
          }

          // Mapper les champs
          $this->mapFieldsRH($node, $row);

        } catch (\Exception $e) {
          $results['errors'][] = $this->t('Erreur ligne @line: @error', [
            '@line' => $line_number,
            '@error' => $e->getMessage(),
          ]);
          $logger->error('Erreur ligne @line: @error', [
            '@line' => $line_number,
            '@error' => $e->getMessage(),
          ]);
        }
      }

      fclose($handle);
      $results['success'] = TRUE;
      $logger->notice('Importation RH terminée: @created créés, @updated mis à jour', [
        '@created' => $results['created'],
        '@updated' => $results['updated'],
      ]);

    } else {
      $results['errors'][] = $this->t('Impossible d\'ouvrir le fichier CSV.');
      $logger->error('Impossible d\'ouvrir le fichier: @file', ['@file' => $file_path]);
    }

    return $results;
  }

  /**
   * Mappe les champs du CSV RH vers les champs Drupal.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Le nœud à mettre à jour.
   * @param array $row
   *   Les données de la ligne CSV.
   */
  protected function mapFieldsRH(Node $node, array $row) {
    $logger = $this->loggerFactory->get('import_reglementation');

    // Langue par défaut
    $node->set('langcode', 'fr');

    // Titre français - utiliser le numéro de texte si l'intitulé est manquant
    $title_fr = trim($row['Intitulé en Français']);
    if (empty($title_fr) || $title_fr === '-' || $title_fr === '' || strtolower($title_fr) === 'neant') {
      $title_fr = $row['N° du texte'];
      $logger->notice('Intitulé français manquant, utilisation du numéro de texte: @numero', [
        '@numero' => $title_fr,
      ]);
    }

    // Limiter la longueur du titre
    if (mb_strlen($title_fr) > 255) {
      $title_fr = mb_substr($title_fr, 0, 252) . '...';
    }
    $node->setTitle($title_fr);

    // Numéro de texte
    $node->set('field_numero_de_text', $row['N° du texte']);

    // Type de réglementation
    if (!empty($row['Type'])) {
      // Gérer les colonnes AR multiples - prendre la première colonne AR après Type
      $type_ar = '';
      if (!empty($row['AR'])) {
        $type_ar = $row['AR'];
      }
      $type_tid = $this->getOrCreateTerm('type_reglementation', $row['Type'], $type_ar);
      if ($type_tid !== null) {
        $node->set('field_type_loi', $type_tid);
      }
    }

    // Sous-secteur
    if (!empty($row['Sous secteur'])) {
      $secteur_tid = $this->getOrCreateTerm('modes_de_transport', $row['Sous secteur']);
      if ($secteur_tid !== null) {
        $node->set('field_secteur', $secteur_tid);
      }
    }

    // Thème
    if (!empty($row['Thème'])) {
      $theme_tid = $this->getOrCreateTerm('domaines_d_activites', $row['Thème']);
      if ($theme_tid !== null) {
        $node->set('field_domaine_d_activite', $theme_tid);
      }
    }

    // Date de publication
    if (!empty($row['Date De publication'])) {
      $date_formatted = $this->parseDate($row['Date De publication']);
      if ($date_formatted) {
        $node->set('field_date', $date_formatted);
      }
    }

    // Sauvegarder le nœud
    $node->save();
    $logger->notice('Nœud sauvegardé: @id', ['@id' => $node->id()]);

    // Ajouter la traduction arabe si disponible
    if (!empty($row['Intitulé en Arabe'])) {
      $this->addArabicTranslationRH($node, $row);
    }
  }

  /**
   * Ajoute une traduction arabe pour un nœud.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Le nœud à traduire.
   * @param array $row
   *   Les données de la ligne CSV.
   */
  protected function addArabicTranslationRH(Node $node, array $row) {
    try {
      $logger = $this->loggerFactory->get('import_reglementation');

      // Vérifier si une traduction arabe existe déjà
      if ($node->hasTranslation('ar')) {
        $translation = $node->getTranslation('ar');
        $logger->notice('Mise à jour traduction arabe existante: @id', ['@id' => $node->id()]);
      } else {
        $translation = $node->addTranslation('ar');
        $logger->notice('Création nouvelle traduction arabe: @id', ['@id' => $node->id()]);
      }

      // Titre arabe - utiliser le numéro de texte si l'intitulé est manquant
      $title_ar = trim($row['Intitulé en Arabe']);
      if (empty($title_ar) || $title_ar === '-' || $title_ar === '' || strtolower($title_ar) === 'neant') {
        $title_ar = $row['N° du texte'];
        $logger->notice('Intitulé arabe manquant, utilisation du numéro de texte: @numero', [
          '@numero' => $title_ar,
        ]);
      }

      if (mb_strlen($title_ar) > 255) {
        $title_ar = mb_substr($title_ar, 0, 252) . '...';
      }
      $translation->setTitle($title_ar);

      // Copier les autres champs
      $translation->set('field_numero_de_text', $row['N° du texte']);

      // Copier les références de taxonomie
      if ($node->hasField('field_type_loi') && !$node->get('field_type_loi')->isEmpty()) {
        $translation->set('field_type_loi', $node->get('field_type_loi')->getValue());
      }

      if ($node->hasField('field_secteur') && !$node->get('field_secteur')->isEmpty()) {
        $translation->set('field_secteur', $node->get('field_secteur')->getValue());
      }

      if ($node->hasField('field_domaine_d_activite') && !$node->get('field_domaine_d_activite')->isEmpty()) {
        $translation->set('field_domaine_d_activite', $node->get('field_domaine_d_activite')->getValue());
      }

      if ($node->hasField('field_date') && !$node->get('field_date')->isEmpty()) {
        $translation->set('field_date', $node->get('field_date')->getValue());
      }

      $translation->save();
      $logger->notice('Traduction arabe sauvegardée: @id', ['@id' => $node->id()]);

    } catch (\Exception $e) {
      $this->loggerFactory->get('import_reglementation')->error('Erreur traduction arabe: @error', [
        '@error' => $e->getMessage(),
      ]);
    }
  }

  /**
   * Parse une date depuis différents formats.
   *
   * @param string $date_string
   *   La chaîne de date à parser.
   *
   * @return string|null
   *   La date formatée en Y-m-d ou null si impossible à parser.
   */
  protected function parseDate($date_string) {
    $date_string = trim($date_string);

    if (empty($date_string)) {
      return null;
    }

    $logger = $this->loggerFactory->get('import_reglementation');
    $logger->notice('Parsing date: @date', ['@date' => $date_string]);

    // Format standard DD/MM/YYYY
    if (preg_match('/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/', $date_string, $matches)) {
      $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
      $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
      $year = $matches[3];
      return "{$year}-{$month}-{$day}";
    }

    // Format DD.MM.YYYY
    if (preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $date_string, $matches)) {
      $day = str_pad($matches[1], 2, '0', STR_PAD_LEFT);
      $month = str_pad($matches[2], 2, '0', STR_PAD_LEFT);
      $year = $matches[3];
      return "{$year}-{$month}-{$day}";
    }

    // Format YYYY-MM-DD (déjà correct)
    if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date_string)) {
      return $date_string;
    }

    // Essayer avec DrupalDateTime
    $formats = ['d/m/Y', 'd.m.Y', 'Y-m-d', 'd-m-Y'];
    foreach ($formats as $format) {
      try {
        $date = DrupalDateTime::createFromFormat($format, $date_string);
        if ($date && !array_sum($date->getErrors())) {
          return $date->format('Y-m-d');
        }
      } catch (\Exception $e) {
        // Continuer avec le format suivant
      }
    }

    $logger->warning('Format de date non reconnu: @date', ['@date' => $date_string]);
    return null;
  }

  /**
   * Récupère ou crée un terme de taxonomie.
   *
   * @param string $vocabulary
   *   Le vocabulaire cible.
   * @param string $name
   *   Le nom du terme en français.
   * @param string $name_ar
   *   Le nom du terme en arabe (optionnel).
   *
   * @return int|null
   *   L'ID du terme ou null en cas d'erreur.
   */
  protected function getOrCreateTerm($vocabulary, $name, $name_ar = '') {
    $name = $this->sanitizeInput($name);
    $vocabulary = $this->sanitizeInput($vocabulary);

    if (empty($name) || empty($vocabulary)) {
      return null;
    }

    // Vérifier le cache
    $cache_key = $vocabulary . ':' . $name;
    if (isset($this->termCache[$cache_key])) {
      return $this->termCache[$cache_key];
    }

    try {
      $logger = $this->loggerFactory->get('import_reglementation');

      // Mapping des vocabulaires
      $vocabulary_mapping = [
        'type_reglementation' => 'type_reglementation',
        'modes_de_transport' => 'modes_de_transport',
        'domaines_d_activites' => 'domaines_d_activites',
      ];

      if (isset($vocabulary_mapping[$vocabulary])) {
        $vocabulary = $vocabulary_mapping[$vocabulary];
      }

      // Rechercher le terme existant
      $terms = $this->entityTypeManager
        ->getStorage('taxonomy_term')
        ->loadByProperties(['name' => $name, 'vid' => $vocabulary]);

      if (!empty($terms)) {
        $term = reset($terms);
        $term_id = $term->id();
        $logger->notice('Terme existant trouvé: @name (@id)', ['@name' => $name, '@id' => $term_id]);
      } else {
        // Créer un nouveau terme
        $term = $this->entityTypeManager
          ->getStorage('taxonomy_term')
          ->create([
            'vid' => $vocabulary,
            'name' => $name,
            'langcode' => 'fr',
          ]);

        $term->save();
        $term_id = $term->id();
        $logger->notice('Nouveau terme créé: @name (@id)', ['@name' => $name, '@id' => $term_id]);

        // Ajouter traduction arabe si disponible
        if (!empty($name_ar) && $name_ar !== $name) {
          try {
            $translation = $term->addTranslation('ar');
            $translation->setName($name_ar);
            $translation->save();
            $logger->notice('Traduction arabe ajoutée pour le terme: @name_ar', ['@name_ar' => $name_ar]);
          } catch (\Exception $e) {
            $logger->warning('Impossible d\'ajouter la traduction arabe: @error', ['@error' => $e->getMessage()]);
          }
        }
      }

      // Mettre en cache
      $this->termCache[$cache_key] = $term_id;
      return $term_id;

    } catch (\Exception $e) {
      $this->loggerFactory->get('import_reglementation')->error('Erreur lors de la création du terme: @error', [
        '@error' => $e->getMessage(),
      ]);
      return null;
    }
  }

  /**
   * Nettoie une chaîne d'entrée.
   *
   * @param string $input
   *   La chaîne à nettoyer.
   *
   * @return string
   *   La chaîne nettoyée.
   */
  protected function sanitizeInput($input) {
    if (!is_string($input)) {
      return $input;
    }

    // Supprimer les espaces en début et fin
    $input = trim($input);

    // Supprimer les caractères de contrôle dangereux
    $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);

    return $input;
  }

}
