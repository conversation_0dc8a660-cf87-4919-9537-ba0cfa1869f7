<?php

namespace Drupal\import_reglementation\Service;

use <PERSON><PERSON><PERSON>\Core\File\FileSystemInterface;
use <PERSON><PERSON><PERSON>\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\Core\StringTranslation\StringTranslationTrait;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\file\Entity\File;
use <PERSON>upal\node\Entity\Node;

/**
 * Service pour organiser les fichiers PDF selon la langue, type de réglementation et numéro de texte.
 */
class PdfOrganizer {
  use StringTranslationTrait;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * Chemin de base pour les fichiers PDF.
   *
   * @var string
   */
  protected $basePdfPath;

  /**
   * Types de réglementation supportés.
   *
   * @var array
   */
  protected $supportedTypes = [
    'Arrêté',
    'Loi',
    'Décret',
    'Dahir',
    'Circulaire',
    'Décision',
    'Cahiers des charges',
    'Cahier des charges',
    'CDC',
  ];

  /**
   * Langues supportées.
   *
   * @var array
   */
  protected $supportedLanguages = [
    'Fr' => 'Fr',
    'Ar' => 'AR',
    'français' => 'Fr',
    'francais' => 'Fr',
    'arabe' => 'AR',
  ];

  /**
   * Constructs a new PdfOrganizer object.
   *
   * @param \Drupal\Core\File\FileSystemInterface $file_system
   *   The file system service.
   * @param \Drupal\Core\Logger\LoggerChannelFactoryInterface $logger_factory
   *   The logger factory.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   */
  public function __construct(
    FileSystemInterface $file_system,
    LoggerChannelFactoryInterface $logger_factory,
    EntityTypeManagerInterface $entity_type_manager
  ) {
    $this->fileSystem = $file_system;
    $this->loggerFactory = $logger_factory;
    $this->entityTypeManager = $entity_type_manager;

    // Définir le chemin de base pour les PDF
    $module_path = \Drupal::service('extension.list.module')->getPath('import_reglementation');
    $this->basePdfPath = \Drupal::service('file_system')->realpath($module_path . '/pdf');
  }

  /**
   * Analyse un nom de fichier PDF pour extraire les informations.
   *
   * @param string $filename
   *   Le nom du fichier (avec ou sans extension).
   *
   * @return array
   *   Tableau contenant:
   *   - 'type': Type de réglementation
   *   - 'numero': Numéro de texte
   *   - 'langue': Langue (Fr/AR)
   *   - 'valid': Boolean indiquant si l'analyse a réussi
   *   - 'original_filename': Nom de fichier original
   */
  public function analyzeFilename($filename) {
    $logger = $this->loggerFactory->get('import_reglementation');

    $result = [
      'type' => '',
      'numero' => '',
      'langue' => '',
      'valid' => FALSE,
      'original_filename' => $filename,
    ];

    // Nettoyer le nom de fichier (enlever l'extension)
    $clean_filename = preg_replace('/\.(pdf|PDF)$/', '', $filename);

    $logger->notice('Analyse du fichier: @filename', ['@filename' => $clean_filename]);

    // Pattern principal pour analyser le nom de fichier
    // Exemples: "Arrêté 20.80 Fr", "Loi 116.14 Ar", "Décret 2.10.311 Fr"
    $patterns = [
      // Pattern 1: Type + Numéro + Langue (ex: "Arrêté 20.80 Fr")
      '/^([A-Za-zÀ-ÿ\s]+?)\s+([0-9]+(?:\.[0-9]+)*)\s+(Fr|Ar|AR)$/i',
      // Pattern 2: Type + Numéro + Langue avec underscore (ex: "Loi_116.14_Fr")
      '/^([A-Za-zÀ-ÿ\s]+?)_([0-9]+(?:\.[0-9]+)*)_+(Fr|Ar|AR)$/i',
      // Pattern 3: Type_Test_Numéro_Langue (ex: "Arrêté_Test_123.45_Fr")
      '/^([A-Za-zÀ-ÿ\s]+?)_[A-Za-z]+_([0-9]+(?:\.[0-9]+)*)_+(Fr|Ar|AR)$/i',
      // Pattern 4: Type + Numéro sans langue explicite (déduire de la position)
      '/^([A-Za-zÀ-ÿ\s]+?)\s+([0-9]+(?:\.[0-9]+)*)$/i',
    ];

    foreach ($patterns as $pattern) {
      if (preg_match($pattern, $clean_filename, $matches)) {
        $type_candidate = trim($matches[1]);
        $numero = trim($matches[2]);
        $langue = isset($matches[3]) ? trim($matches[3]) : '';

        $logger->notice('Pattern trouvé - Type: @type, Numéro: @numero, Langue: @langue', [
          '@type' => $type_candidate,
          '@numero' => $numero,
          '@langue' => $langue,
        ]);

        // Vérifier si le type est supporté
        $type_found = $this->findSupportedType($type_candidate);
        if ($type_found) {
          $result['type'] = $type_found;
          $result['numero'] = $numero;

          // Normaliser la langue
          if (!empty($langue)) {
            $result['langue'] = $this->normalizeLanguage($langue);
          } else {
            // Si pas de langue explicite, essayer de la déduire du contexte
            $result['langue'] = $this->deduceLanguageFromContext($clean_filename);
          }

          $result['valid'] = TRUE;

          $logger->notice('Analyse réussie - Type: @type, Numéro: @numero, Langue: @langue', [
            '@type' => $result['type'],
            '@numero' => $result['numero'],
            '@langue' => $result['langue'],
          ]);

          break;
        }
      }
    }

    if (!$result['valid']) {
      $logger->warning('Impossible d\'analyser le fichier: @filename', ['@filename' => $filename]);
    }

    return $result;
  }

  /**
   * Trouve le type de réglementation supporté correspondant.
   *
   * @param string $type_candidate
   *   Le type candidat extrait du nom de fichier.
   *
   * @return string|null
   *   Le type supporté ou null si non trouvé.
   */
  protected function findSupportedType($type_candidate) {
    $type_candidate = trim($type_candidate);

    // Recherche exacte d'abord
    foreach ($this->supportedTypes as $supported_type) {
      if (strcasecmp($type_candidate, $supported_type) === 0) {
        return $supported_type;
      }
    }

    // Recherche partielle (pour gérer les variations)
    foreach ($this->supportedTypes as $supported_type) {
      if (stripos($type_candidate, $supported_type) !== FALSE ||
          stripos($supported_type, $type_candidate) !== FALSE) {
        return $supported_type;
      }
    }

    // Cas spéciaux
    $special_cases = [
      'Arrete' => 'Arrêté',
      'Decret' => 'Décret',
      'Decision' => 'Décision',
      'CDC' => 'Cahiers des charges',
    ];

    foreach ($special_cases as $variant => $canonical) {
      if (strcasecmp($type_candidate, $variant) === 0) {
        return $canonical;
      }
    }

    return null;
  }

  /**
   * Normalise la langue.
   *
   * @param string $langue
   *   La langue à normaliser.
   *
   * @return string
   *   La langue normalisée (Fr ou AR).
   */
  protected function normalizeLanguage($langue) {
    $langue = trim($langue);

    foreach ($this->supportedLanguages as $variant => $canonical) {
      if (strcasecmp($langue, $variant) === 0) {
        return $canonical;
      }
    }

    // Par défaut, retourner français
    return 'Fr';
  }

  /**
   * Déduit la langue à partir du contexte du nom de fichier.
   *
   * @param string $filename
   *   Le nom de fichier.
   *
   * @return string
   *   La langue déduite (Fr ou AR).
   */
  protected function deduceLanguageFromContext($filename) {
    // Rechercher des indicateurs de langue dans le nom de fichier
    if (preg_match('/\b(ar|arabe|arabic)\b/i', $filename)) {
      return 'AR';
    }

    if (preg_match('/\b(fr|francais|français|french)\b/i', $filename)) {
      return 'Fr';
    }

    // Par défaut, français
    return 'Fr';
  }

  /**
   * Génère le chemin de destination pour un fichier PDF.
   *
   * @param array $file_info
   *   Informations du fichier retournées par analyzeFilename().
   *
   * @return string|null
   *   Le chemin de destination ou null si les informations sont invalides.
   */
  public function generateDestinationPath($file_info) {
    if (!$file_info['valid']) {
      return null;
    }

    $path_parts = [
      $this->basePdfPath,
      $file_info['langue'],
      $file_info['type'],
    ];

    return implode(DIRECTORY_SEPARATOR, $path_parts);
  }

  /**
   * Crée les dossiers nécessaires pour organiser un fichier.
   *
   * @param string $destination_path
   *   Le chemin de destination.
   *
   * @return bool
   *   TRUE si les dossiers ont été créés avec succès, FALSE sinon.
   */
  public function createDirectories($destination_path) {
    $logger = $this->loggerFactory->get('import_reglementation');

    try {
      if (!is_dir($destination_path)) {
        $result = $this->fileSystem->prepareDirectory(
          $destination_path,
          FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS
        );

        if ($result) {
          $logger->notice('Dossier créé: @path', ['@path' => $destination_path]);
          return TRUE;
        } else {
          $logger->error('Impossible de créer le dossier: @path', ['@path' => $destination_path]);
          return FALSE;
        }
      }

      return TRUE;
    } catch (\Exception $e) {
      $logger->error('Erreur lors de la création du dossier @path: @error', [
        '@path' => $destination_path,
        '@error' => $e->getMessage(),
      ]);
      return FALSE;
    }
  }

  /**
   * Organise un fichier PDF dans la structure appropriée.
   *
   * @param string $source_file_path
   *   Chemin complet vers le fichier source.
   * @param bool $move
   *   TRUE pour déplacer le fichier, FALSE pour le copier.
   *
   * @return array
   *   Résultat de l'opération avec 'success', 'message', 'destination_path'.
   */
  public function organizeFile($source_file_path, $move = FALSE) {
    $logger = $this->loggerFactory->get('import_reglementation');

    $result = [
      'success' => FALSE,
      'message' => '',
      'destination_path' => '',
      'file_info' => [],
    ];

    // Vérifier que le fichier source existe
    if (!file_exists($source_file_path)) {
      $result['message'] = $this->t('Le fichier source n\'existe pas: @path', ['@path' => $source_file_path]);
      $logger->error($result['message']);
      return $result;
    }

    // Extraire le nom de fichier
    $filename = basename($source_file_path);

    // Analyser le nom de fichier
    $file_info = $this->analyzeFilename($filename);
    $result['file_info'] = $file_info;

    if (!$file_info['valid']) {
      $result['message'] = $this->t('Impossible d\'analyser le nom de fichier: @filename', ['@filename' => $filename]);
      $logger->warning($result['message']);
      return $result;
    }

    // Générer le chemin de destination
    $destination_dir = $this->generateDestinationPath($file_info);
    if (!$destination_dir) {
      $result['message'] = $this->t('Impossible de générer le chemin de destination pour: @filename', ['@filename' => $filename]);
      $logger->error($result['message']);
      return $result;
    }

    // Créer les dossiers nécessaires
    if (!$this->createDirectories($destination_dir)) {
      $result['message'] = $this->t('Impossible de créer les dossiers de destination: @path', ['@path' => $destination_dir]);
      $logger->error($result['message']);
      return $result;
    }

    // Chemin complet de destination
    $destination_file_path = $destination_dir . DIRECTORY_SEPARATOR . $filename;
    $result['destination_path'] = $destination_file_path;

    try {
      if ($move) {
        // Déplacer le fichier
        if (rename($source_file_path, $destination_file_path)) {
          $result['success'] = TRUE;
          $result['message'] = $this->t('Fichier déplacé avec succès vers: @path', ['@path' => $destination_file_path]);
          $logger->notice('Fichier déplacé: @source -> @dest', [
            '@source' => $source_file_path,
            '@dest' => $destination_file_path,
          ]);
        } else {
          $result['message'] = $this->t('Erreur lors du déplacement du fichier');
          $logger->error('Erreur lors du déplacement: @source -> @dest', [
            '@source' => $source_file_path,
            '@dest' => $destination_file_path,
          ]);
        }
      } else {
        // Copier le fichier
        if (copy($source_file_path, $destination_file_path)) {
          $result['success'] = TRUE;
          $result['message'] = $this->t('Fichier copié avec succès vers: @path', ['@path' => $destination_file_path]);
          $logger->notice('Fichier copié: @source -> @dest', [
            '@source' => $source_file_path,
            '@dest' => $destination_file_path,
          ]);
        } else {
          $result['message'] = $this->t('Erreur lors de la copie du fichier');
          $logger->error('Erreur lors de la copie: @source -> @dest', [
            '@source' => $source_file_path,
            '@dest' => $destination_file_path,
          ]);
        }
      }
    } catch (\Exception $e) {
      $result['message'] = $this->t('Exception lors de l\'opération sur le fichier: @error', ['@error' => $e->getMessage()]);
      $logger->error('Exception: @error', ['@error' => $e->getMessage()]);
    }

    return $result;
  }

  /**
   * Organise tous les fichiers PDF d'un dossier.
   *
   * @param string $source_directory
   *   Dossier source contenant les fichiers PDF.
   * @param bool $move
   *   TRUE pour déplacer les fichiers, FALSE pour les copier.
   * @param bool $recursive
   *   TRUE pour traiter les sous-dossiers récursivement.
   *
   * @return array
   *   Résultats de l'opération avec statistiques.
   */
  public function organizeDirectory($source_directory, $move = FALSE, $recursive = FALSE) {
    $logger = $this->loggerFactory->get('import_reglementation');

    $results = [
      'success' => 0,
      'errors' => 0,
      'skipped' => 0,
      'total' => 0,
      'details' => [],
    ];

    if (!is_dir($source_directory)) {
      $logger->error('Le dossier source n\'existe pas: @path', ['@path' => $source_directory]);
      return $results;
    }

    $iterator = $recursive ?
      new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($source_directory)) :
      new \DirectoryIterator($source_directory);

    foreach ($iterator as $file) {
      if ($file->isDot()) {
        continue;
      }

      if ($file->isFile() && preg_match('/\.pdf$/i', $file->getFilename())) {
        $results['total']++;
        $file_path = $file->getRealPath();

        $result = $this->organizeFile($file_path, $move);

        if ($result['success']) {
          $results['success']++;
        } else {
          $results['errors']++;
        }

        $results['details'][] = [
          'file' => $file->getFilename(),
          'result' => $result,
        ];

        $logger->notice('Traitement du fichier @file: @status', [
          '@file' => $file->getFilename(),
          '@status' => $result['success'] ? 'Succès' : 'Erreur',
        ]);
      }
    }

    $logger->notice('Organisation terminée - Total: @total, Succès: @success, Erreurs: @errors', [
      '@total' => $results['total'],
      '@success' => $results['success'],
      '@errors' => $results['errors'],
    ]);

    return $results;
  }

  /**
   * Trouve un fichier PDF par son numéro de texte et type.
   *
   * @param string $numero
   *   Numéro de texte à rechercher.
   * @param string $type
   *   Type de réglementation.
   * @param string $langue
   *   Langue (optionnel).
   *
   * @return array
   *   Liste des fichiers trouvés avec leurs chemins complets.
   */
  public function findFileByNumber($numero, $type, $langue = '') {
    $found_files = [];

    $search_paths = [];

    if (!empty($langue)) {
      // Recherche dans une langue spécifique
      $search_paths[] = $this->basePdfPath . DIRECTORY_SEPARATOR . $langue . DIRECTORY_SEPARATOR . $type;
    } else {
      // Recherche dans toutes les langues
      foreach (['Fr', 'AR'] as $lang) {
        $search_paths[] = $this->basePdfPath . DIRECTORY_SEPARATOR . $lang . DIRECTORY_SEPARATOR . $type;
      }
    }

    foreach ($search_paths as $search_path) {
      if (is_dir($search_path)) {
        $iterator = new \DirectoryIterator($search_path);

        foreach ($iterator as $file) {
          if ($file->isDot() || !$file->isFile()) {
            continue;
          }

          $filename = $file->getFilename();
          if (preg_match('/\.pdf$/i', $filename)) {
            $file_info = $this->analyzeFilename($filename);

            if ($file_info['valid'] && $file_info['numero'] === $numero && $file_info['type'] === $type) {
              $found_files[] = [
                'path' => $file->getRealPath(),
                'filename' => $filename,
                'info' => $file_info,
              ];
            }
          }
        }
      }
    }

    return $found_files;
  }

  /**
   * Trouve le nœud de réglementation correspondant au fichier PDF.
   *
   * @param array $file_info
   *   Informations du fichier retournées par analyzeFilename().
   *
   * @return \Drupal\node\Entity\Node|null
   *   Le nœud correspondant ou null si non trouvé.
   */
  public function findCorrespondingNode($file_info) {
    $logger = $this->loggerFactory->get('import_reglementation');

    if (!$file_info['valid']) {
      $logger->warning('Informations de fichier invalides pour la recherche de nœud');
      return null;
    }

    try {
      // Convertir la langue du fichier vers le code langue Drupal
      $langcode = $file_info['langue'] === 'Fr' ? 'fr' : 'ar';

      $logger->notice('Recherche du nœud: Type=@type, Numéro=@numero, Langue=@langue', [
        '@type' => $file_info['type'],
        '@numero' => $file_info['numero'],
        '@langue' => $langcode,
      ]);

      // D'abord, chercher les termes de taxonomie correspondants
      $type_term_id = $this->findTaxonomyTermByName('type', $file_info['type']);

      if (!$type_term_id) {
        $logger->warning('Terme de taxonomie non trouvé pour le type: @type', ['@type' => $file_info['type']]);
        return null;
      }

      // D'abord, rechercher le nœud principal (sans contrainte de langue)
      $query = $this->entityTypeManager->getStorage('node')->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation')
        ->condition('field_numero_de_text', $file_info['numero'])
        ->condition('field_type_loi', $type_term_id)
        ->range(0, 1);

      $nids = $query->execute();

      if (!empty($nids)) {
        $nid = reset($nids);
        $node = $this->entityTypeManager->getStorage('node')->load($nid);

        $logger->notice('Nœud principal trouvé: ID=@nid, Langue=@node_lang', [
          '@nid' => $nid,
          '@node_lang' => $node->language()->getId(),
        ]);

        // Vérifier si on a besoin d'une traduction
        if ($node->language()->getId() !== $langcode) {
          // Chercher la traduction dans la langue demandée
          if ($node->hasTranslation($langcode)) {
            $translated_node = $node->getTranslation($langcode);
            $logger->notice('Traduction trouvée: ID=@nid, Langue=@lang, Titre=@title', [
              '@nid' => $nid,
              '@lang' => $langcode,
              '@title' => $translated_node->label(),
            ]);
            return $translated_node;
          } else {
            $logger->warning('Nœud trouvé mais pas de traduction en @langue pour: Type=@type, Numéro=@numero', [
              '@type' => $file_info['type'],
              '@numero' => $file_info['numero'],
              '@langue' => $langcode,
            ]);
            // Retourner le nœud principal même s'il n'y a pas de traduction
            return $node;
          }
        } else {
          // Le nœud est déjà dans la bonne langue
          $logger->notice('Nœud dans la bonne langue: ID=@nid, Titre=@title', [
            '@nid' => $nid,
            '@title' => $node->label(),
          ]);
          return $node;
        }
      } else {
        $logger->warning('Aucun nœud trouvé pour: Type=@type, Numéro=@numero', [
          '@type' => $file_info['type'],
          '@numero' => $file_info['numero'],
        ]);
        return null;
      }
    } catch (\Exception $e) {
      $logger->error('Erreur lors de la recherche du nœud: @error', ['@error' => $e->getMessage()]);
      return null;
    }
  }

  /**
   * Trouve un terme de taxonomie par son nom.
   *
   * @param string $vocabulary
   *   Le vocabulaire de taxonomie.
   * @param string $name
   *   Le nom du terme.
   *
   * @return int|null
   *   L'ID du terme ou null si non trouvé.
   */
  protected function findTaxonomyTermByName($vocabulary, $name) {
    try {
      $terms = $this->entityTypeManager
        ->getStorage('taxonomy_term')
        ->loadByProperties([
          'name' => $name,
          'vid' => $vocabulary,
        ]);

      if (!empty($terms)) {
        $term = reset($terms);
        return $term->id();
      }

      return null;
    } catch (\Exception $e) {
      $this->loggerFactory->get('import_reglementation')
        ->error('Erreur lors de la recherche du terme: @error', ['@error' => $e->getMessage()]);
      return null;
    }
  }

  /**
   * Attache un fichier PDF au champ field_lien_telechargement d'un nœud.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Le nœud de réglementation.
   * @param string $file_path
   *   Chemin vers le fichier PDF.
   *
   * @return array
   *   Résultat de l'opération avec 'success' et 'message'.
   */
  public function attachPdfToNode(Node $node, $file_path) {
    $logger = $this->loggerFactory->get('import_reglementation');

    $result = [
      'success' => FALSE,
      'message' => '',
      'file_id' => null,
    ];

    try {
      // Vérifier que le fichier existe
      if (!file_exists($file_path)) {
        $result['message'] = $this->t('Le fichier n\'existe pas: @path', ['@path' => $file_path]);
        return $result;
      }

      // Créer le répertoire de destination dans files
      $destination_dir = 'public://reglementation/pdf';
      $this->fileSystem->prepareDirectory($destination_dir, FileSystemInterface::CREATE_DIRECTORY | FileSystemInterface::MODIFY_PERMISSIONS);

      // Copier le fichier vers le répertoire files de Drupal
      $filename = basename($file_path);
      $destination = $destination_dir . '/' . $filename;

      $file_uri = $this->fileSystem->copy($file_path, $destination);

      if (!$file_uri) {
        $result['message'] = $this->t('Impossible de copier le fichier vers: @dest', ['@dest' => $destination]);
        return $result;
      }

      // Créer l'entité File
      $file = File::create([
        'filename' => $filename,
        'uri' => $file_uri,
        'status' => 1,
        'uid' => 1, // Utilisateur admin
      ]);
      $file->save();

      $result['file_id'] = $file->id();

      // Attacher le fichier au nœud
      $node->set('field_lien_telechargement', [
        'target_id' => $file->id(),
        'description' => $filename,
      ]);

      $node->save();

      $result['success'] = TRUE;
      $result['message'] = $this->t('Fichier PDF attaché avec succès au nœud @nid', ['@nid' => $node->id()]);

      $logger->notice('Fichier PDF attaché: @file -> Nœud @nid', [
        '@file' => $filename,
        '@nid' => $node->id(),
      ]);

    } catch (\Exception $e) {
      $result['message'] = $this->t('Erreur lors de l\'attachement du fichier: @error', ['@error' => $e->getMessage()]);
      $logger->error('Erreur lors de l\'attachement du fichier: @error', ['@error' => $e->getMessage()]);
    }

    return $result;
  }

  /**
   * Traite un fichier PDF complet : analyse, trouve le nœud et attache le fichier.
   *
   * @param string $file_path
   *   Chemin vers le fichier PDF.
   *
   * @return array
   *   Résultat de l'opération avec détails.
   */
  public function processPdfFile($file_path) {
    $logger = $this->loggerFactory->get('import_reglementation');

    $result = [
      'success' => FALSE,
      'message' => '',
      'file_info' => [],
      'node_found' => FALSE,
      'node_id' => null,
      'file_attached' => FALSE,
      'file_id' => null,
    ];

    // 1. Analyser le nom de fichier
    $filename = basename($file_path);
    $file_info = $this->analyzeFilename($filename);
    $result['file_info'] = $file_info;

    if (!$file_info['valid']) {
      $result['message'] = $this->t('Impossible d\'analyser le nom de fichier: @filename', ['@filename' => $filename]);
      return $result;
    }

    // 2. Trouver le nœud correspondant
    $node = $this->findCorrespondingNode($file_info);

    if (!$node) {
      $result['message'] = $this->t('Aucun nœud de réglementation trouvé pour: @type @numero (@langue)', [
        '@type' => $file_info['type'],
        '@numero' => $file_info['numero'],
        '@langue' => $file_info['langue'],
      ]);
      return $result;
    }

    $result['node_found'] = TRUE;
    $result['node_id'] = $node->id();

    // 3. Attacher le fichier PDF au nœud
    $attach_result = $this->attachPdfToNode($node, $file_path);

    if ($attach_result['success']) {
      $result['success'] = TRUE;
      $result['file_attached'] = TRUE;
      $result['file_id'] = $attach_result['file_id'];
      $result['message'] = $this->t('Fichier PDF traité avec succès: @filename attaché au nœud @nid', [
        '@filename' => $filename,
        '@nid' => $node->id(),
      ]);

      $logger->notice('Traitement PDF réussi: @filename -> Nœud @nid', [
        '@filename' => $filename,
        '@nid' => $node->id(),
      ]);
    } else {
      $result['message'] = $attach_result['message'];
    }

    return $result;
  }

  /**
   * Obtient des informations sur les traductions disponibles d'un nœud.
   *
   * @param \Drupal\node\Entity\Node $node
   *   Le nœud à analyser.
   *
   * @return array
   *   Informations sur les traductions disponibles.
   */
  public function getNodeTranslationInfo(Node $node) {
    $info = [
      'original_language' => $node->language()->getId(),
      'original_title' => $node->label(),
      'translations' => [],
      'has_french' => FALSE,
      'has_arabic' => FALSE,
    ];

    // Vérifier les traductions disponibles
    $languages = ['fr', 'ar'];

    foreach ($languages as $langcode) {
      if ($node->hasTranslation($langcode)) {
        $translation = $node->getTranslation($langcode);
        $info['translations'][$langcode] = [
          'title' => $translation->label(),
          'exists' => TRUE,
        ];

        if ($langcode === 'fr') {
          $info['has_french'] = TRUE;
        }
        if ($langcode === 'ar') {
          $info['has_arabic'] = TRUE;
        }
      } else {
        $info['translations'][$langcode] = [
          'title' => null,
          'exists' => FALSE,
        ];
      }
    }

    return $info;
  }

  /**
   * Traite plusieurs fichiers PDF et génère un rapport détaillé.
   *
   * @param array $file_paths
   *   Liste des chemins vers les fichiers PDF.
   *
   * @return array
   *   Rapport détaillé du traitement.
   */
  public function processPdfFiles(array $file_paths) {
    $logger = $this->loggerFactory->get('import_reglementation');

    $report = [
      'total_files' => count($file_paths),
      'processed' => 0,
      'successful' => 0,
      'nodes_found' => 0,
      'files_attached' => 0,
      'errors' => 0,
      'details' => [],
      'summary' => [],
    ];

    foreach ($file_paths as $file_path) {
      $filename = basename($file_path);
      $report['processed']++;

      $logger->notice('Traitement du fichier @file (@current/@total)', [
        '@file' => $filename,
        '@current' => $report['processed'],
        '@total' => $report['total_files'],
      ]);

      $result = $this->processPdfFile($file_path);

      if ($result['success']) {
        $report['successful']++;
      } else {
        $report['errors']++;
      }

      if ($result['node_found']) {
        $report['nodes_found']++;
      }

      if ($result['file_attached']) {
        $report['files_attached']++;
      }

      $report['details'][] = [
        'filename' => $filename,
        'file_path' => $file_path,
        'result' => $result,
      ];
    }

    // Générer un résumé
    $report['summary'] = [
      'success_rate' => $report['total_files'] > 0 ? round(($report['successful'] / $report['total_files']) * 100, 2) : 0,
      'node_match_rate' => $report['total_files'] > 0 ? round(($report['nodes_found'] / $report['total_files']) * 100, 2) : 0,
      'attachment_rate' => $report['nodes_found'] > 0 ? round(($report['files_attached'] / $report['nodes_found']) * 100, 2) : 0,
    ];

    $logger->notice('Traitement terminé: @successful/@total fichiers traités avec succès', [
      '@successful' => $report['successful'],
      '@total' => $report['total_files'],
    ]);

    return $report;
  }
}
