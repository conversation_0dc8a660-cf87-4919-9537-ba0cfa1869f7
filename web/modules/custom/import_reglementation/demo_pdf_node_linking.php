<?php

/**
 * Démonstration du service PdfOrganizer pour lier les PDF aux nœuds.
 * 
 * Ce script montre comment utiliser le service pour :
 * 1. Analyser un nom de fichier PDF
 * 2. Trouver le nœud de réglementation correspondant
 * 3. Attacher le fichier PDF au champ field_lien_telechargement
 */

// Charger Drupal
$autoloader = require_once '/var/www/html/mtl/vendor/autoload.php';

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

$kernel = new DrupalKernel('prod', $autoloader);
$request = Request::createFromGlobals();
$response = $kernel->handle($request);
$kernel->boot();

// Obtenir le service PdfOrganizer
$pdf_organizer = \Drupal::service('import_reglementation.pdf_organizer');

echo "=== Démonstration du lien PDF-Nœud ===\n\n";

// Test 1: Analyser et chercher des nœuds pour des fichiers existants
echo "1. Test de recherche de nœuds pour des fichiers PDF existants:\n";
echo "==========================================================\n";

$test_files = [
    'Arrêté 20.80 Fr.pdf',
    'Loi 116.14 Fr.pdf',
    'Décret 2.10.311 Fr.pdf',
    'Arrêté 20.80 Ar.pdf',
];

foreach ($test_files as $filename) {
    echo "\n--- Test avec: $filename ---\n";
    
    // 1. Analyser le fichier
    $file_info = $pdf_organizer->analyzeFilename($filename);
    
    if ($file_info['valid']) {
        echo "✓ Analyse réussie:\n";
        echo "  - Type: {$file_info['type']}\n";
        echo "  - Numéro: {$file_info['numero']}\n";
        echo "  - Langue: {$file_info['langue']}\n";
        
        // 2. Chercher le nœud correspondant
        $node = $pdf_organizer->findCorrespondingNode($file_info);
        
        if ($node) {
            echo "✓ Nœud trouvé:\n";
            echo "  - ID: {$node->id()}\n";
            echo "  - Titre: {$node->label()}\n";
            echo "  - Langue: {$node->get('langcode')->value}\n";
            echo "  - URL: /node/{$node->id()}\n";
            
            // Vérifier si un fichier est déjà attaché
            $current_file = $node->get('field_lien_telechargement')->entity;
            if ($current_file) {
                echo "  - Fichier actuel: {$current_file->getFilename()}\n";
            } else {
                echo "  - Aucun fichier attaché actuellement\n";
            }
        } else {
            echo "✗ Aucun nœud correspondant trouvé\n";
        }
    } else {
        echo "✗ Impossible d'analyser le nom de fichier\n";
    }
}

echo "\n\n2. Test de traitement complet d'un fichier PDF:\n";
echo "==============================================\n";

// Créer un fichier de test
$test_file = '/tmp/Demo_Arrêté_999.99_Fr.pdf';
file_put_contents($test_file, '%PDF-1.4 Fichier de démonstration pour test');
echo "Fichier de test créé: $test_file\n";

// Analyser le fichier
$file_info = $pdf_organizer->analyzeFilename(basename($test_file));
echo "\nAnalyse du fichier de test:\n";
if ($file_info['valid']) {
    echo "  - Type: {$file_info['type']}\n";
    echo "  - Numéro: {$file_info['numero']}\n";
    echo "  - Langue: {$file_info['langue']}\n";
    
    // Chercher le nœud (ne devrait pas exister)
    $node = $pdf_organizer->findCorrespondingNode($file_info);
    if ($node) {
        echo "  - Nœud trouvé: {$node->id()}\n";
    } else {
        echo "  - Aucun nœud correspondant (normal pour ce test)\n";
    }
} else {
    echo "  - Erreur d'analyse\n";
}

// Nettoyer
if (file_exists($test_file)) {
    unlink($test_file);
    echo "\nFichier de test supprimé.\n";
}

echo "\n\n3. Statistiques des nœuds de réglementation:\n";
echo "==========================================\n";

try {
    // Compter les nœuds de réglementation par langue
    $entity_type_manager = \Drupal::entityTypeManager();
    
    $query_fr = $entity_type_manager->getStorage('node')->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation')
        ->condition('langcode', 'fr');
    $count_fr = $query_fr->count()->execute();
    
    $query_ar = $entity_type_manager->getStorage('node')->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation')
        ->condition('langcode', 'ar');
    $count_ar = $query_ar->count()->execute();
    
    echo "Nœuds de réglementation dans la base de données:\n";
    echo "  - Français: $count_fr nœuds\n";
    echo "  - Arabe: $count_ar nœuds\n";
    echo "  - Total: " . ($count_fr + $count_ar) . " nœuds\n";
    
    // Compter les nœuds avec des fichiers attachés
    $query_with_files = $entity_type_manager->getStorage('node')->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'reglementation')
        ->exists('field_lien_telechargement');
    $count_with_files = $query_with_files->count()->execute();
    
    echo "  - Avec fichiers attachés: $count_with_files nœuds\n";
    echo "  - Sans fichiers: " . ($count_fr + $count_ar - $count_with_files) . " nœuds\n";
    
} catch (\Exception $e) {
    echo "Erreur lors du comptage: " . $e->getMessage() . "\n";
}

echo "\n\n4. Exemples d'utilisation du service:\n";
echo "====================================\n";

echo "
// Analyser un fichier PDF
\$pdf_organizer = \\Drupal::service('import_reglementation.pdf_organizer');
\$file_info = \$pdf_organizer->analyzeFilename('Arrêté 20.80 Fr.pdf');

// Trouver le nœud correspondant
\$node = \$pdf_organizer->findCorrespondingNode(\$file_info);

// Traiter un fichier complet (analyse + recherche + attachement)
\$result = \$pdf_organizer->processPdfFile('/path/to/file.pdf');

// Vérifier le résultat
if (\$result['success']) {
    echo \"Fichier attaché au nœud {\$result['node_id']}\";
}
";

echo "\n\n5. Commandes Drush disponibles:\n";
echo "==============================\n";

echo "
# Analyser un nom de fichier
vendor/bin/drush pdf:analyze \"Arrêté 20.80 Fr.pdf\"

# Trouver le nœud correspondant
vendor/bin/drush pdf:find-node \"Arrêté 20.80 Fr.pdf\"

# Traiter un fichier PDF complet
vendor/bin/drush pdf:process \"/path/to/Arrêté 20.80 Fr.pdf\"

# Traiter tous les PDF d'un dossier
vendor/bin/drush pdf:process-directory /path/to/pdf/directory

# Traiter récursivement
vendor/bin/drush pdf:process-directory /path/to/pdf/directory --recursive
";

echo "\n=== Fin de la démonstration ===\n";
