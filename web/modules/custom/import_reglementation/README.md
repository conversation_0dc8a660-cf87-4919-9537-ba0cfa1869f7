# Module d'Import de Réglementation PDF

Ce module permet d'importer des fichiers PDF vers des nœuds de réglementation existants dans Drupal, avec une prise en charge spéciale pour les fichiers en arabe.

## Fonctionnalités

- Import automatique de fichiers PDF vers les nœuds correspondants
- Détection automatique de la langue (arabe/français)
- Recherche intelligente par numéro de référence et titre
- Support des fichiers PDF en arabe avec noms de fichiers Unicode
- Traitement par lots (batch) pour les gros volumes
- Mode simulation pour tester avant l'import réel

## Résolution de l'erreur 413 "Request Entity Too Large"

Cette erreur survient quand la taille de la requête dépasse les limites du serveur. Voici comment la résoudre :

### 1. Configuration PHP (php.ini)

Augmentez ces valeurs dans votre fichier `php.ini` :

```ini
# Taille maximale des fichiers uploadés
upload_max_filesize = 100M

# Taille maximale des données POST (doit être >= upload_max_filesize)
post_max_size = 120M

# Limite de mémoire PHP
memory_limit = 256M

# Temps d'exécution maximum
max_execution_time = 300

# Nombre maximum de fichiers uploadés simultanément
max_file_uploads = 20
```

### 2. Configuration Nginx

Ajoutez dans votre configuration Nginx :

```nginx
server {
    # Taille maximale du corps de la requête
    client_max_body_size 100M;
    
    # Timeout pour les uploads
    client_body_timeout 300s;
    
    # Buffer pour les gros uploads
    client_body_buffer_size 128k;
}
```

### 3. Configuration Apache (si applicable)

Ajoutez dans votre `.htaccess` ou configuration Apache :

```apache
# Limite de taille des requêtes
LimitRequestBody 104857600

# Timeout
TimeOut 300
```

### 4. Configuration Drupal

Dans `settings.php`, vous pouvez aussi ajouter :

```php
// Augmenter les limites pour les fichiers
ini_set('upload_max_filesize', '100M');
ini_set('post_max_size', '120M');
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);
```

## Conseils pour les fichiers PDF arabes

### Problèmes courants et solutions

1. **Noms de fichiers avec caractères arabes**
   - Le module normalise automatiquement les noms de fichiers
   - Utilisez des noms contenant les numéros de référence (ex: "قانون رقم 31.13")

2. **Fichiers volumineux**
   - Les PDF avec texte arabe peuvent être plus lourds
   - Importez par petits lots (3-5 fichiers à la fois)
   - Utilisez l'option "Taille du lot de traitement" dans le formulaire

3. **Détection de langue**
   - Le système détecte automatiquement l'arabe vs français
   - Vous pouvez forcer une langue dans les options avancées

### Optimisation des performances

1. **Traitement par lots**
   - Utilisez la taille de lot recommandée (3 fichiers)
   - Réduisez à 1 fichier si vous avez des problèmes de mémoire

2. **Mode simulation**
   - Testez d'abord avec le mode simulation activé
   - Vérifiez que les correspondances sont correctes

3. **Surveillance des logs**
   - Consultez les logs Drupal : `/admin/reports/dblog`
   - Filtrez par "import_reglementation" pour voir les détails

## Utilisation

1. Allez sur `/admin/content/import-pdf-zip`
2. Sélectionnez vos fichiers PDF (max 5 à la fois recommandé)
3. Configurez les options si nécessaire
4. Lancez l'import

## Dépannage

### Erreur 413 persiste
- Vérifiez que toutes les configurations ont été appliquées
- Redémarrez PHP-FPM et Nginx/Apache
- Réduisez le nombre de fichiers uploadés simultanément

### Fichiers non trouvés
- Vérifiez que les noms de fichiers contiennent les numéros de référence
- Consultez les logs pour voir les tentatives de recherche
- Utilisez le mode simulation pour diagnostiquer

### Problèmes de mémoire
- Réduisez la taille du lot de traitement à 1
- Augmentez `memory_limit` dans PHP
- Traitez les fichiers par plus petits groupes

## Support

Pour plus d'aide, consultez les logs Drupal ou contactez l'administrateur système. 