# Améliorations du Module Import Réglementation

## Résumé des Modifications

Le module a été amélioré pour mieux utiliser le champ `field_numero_de_text` lors de l'import des fichiers PDF, permettant une correspondance plus précise entre les PDFs et les nœuds de réglementation.

## Principales Améliorations

### 1. Priorité au Champ `field_numero_de_text`

**Avant :** La recherche se basait principalement sur le titre du nœud.

**Maintenant :** La recherche priorise le champ `field_numero_de_text` qui contient les numéros de référence officiels.

**Ordre de recherche :**
1. **PRIORITÉ ABSOLUE** : Correspondance exacte dans `field_numero_de_text`
2. Correspondance LIKE dans `field_numero_de_text` avec scoring
3. Recherche dans le titre du nœud
4. Recherche approximative et par mots-clés

### 2. Extraction Améliorée des Numéros de Référence

**Nouveaux patterns supportés :**
- Numéros avec points : `31.13`, `1.18.15`, `2.21.968`
- <PERSON>um<PERSON>ros avec tirets : `31-13`, `1-18-15` (convertis automatiquement)
- Numéros arabes : `رقم 31.13`, `القانون رقم 1.18.15`
- Numéros simples : `loi 123`, `قانون 456`
- Années : `2021`, `2023`

**Priorisation intelligente :**
- Les numéros avec séparateurs (points/tirets) sont prioritaires
- Tri par longueur pour privilégier les numéros plus spécifiques

### 3. Système de Scoring pour la Correspondance

**Nouveau système de scoring (0.0 à 1.0) :**
- **1.0** : Correspondance exacte
- **0.9** : Numéro contenu dans le champ (avec facteur de longueur)
- **0.8** : Champ contenu dans le numéro
- **0.7** : Correspondance partielle des composants
- **0.5** : Similarité textuelle générale

**Seuil de confiance :** 0.7 (70%) pour accepter une correspondance.

### 4. Normalisation des Numéros de Référence

**Processus de normalisation :**
- Suppression des espaces et caractères spéciaux
- Conversion des tirets et slashes en points
- Nettoyage des points multiples
- Comparaison insensible à la casse

### 5. Recherche Multi-Langues Intelligente

**Stratégie de recherche :**
- Détection automatique de la langue du PDF
- Recherche prioritaire dans la langue détectée
- Fallback vers toutes les langues si nécessaire
- Support amélioré pour l'arabe et le français

## Exemples de Correspondances

### Exemple 1 : PDF Arabe
```
Fichier PDF : "القانون رقم 31.13 المتعلق بالحق في الحصول على المعلومات.pdf"
Numéro extrait : "31.13"
Recherche dans : field_numero_de_text = "31.13"
Résultat : Correspondance exacte (Score: 1.0)
```

### Exemple 2 : PDF Français avec Tirets
```
Fichier PDF : "dahir-1-58-008_fr.pdf"
Numéro extrait : "1-58-008" → normalisé en "1.58.008"
Recherche dans : field_numero_de_text = "1.58.008"
Résultat : Correspondance exacte (Score: 1.0)
```

### Exemple 3 : Correspondance Partielle
```
Fichier PDF : "loi_123_2023.pdf"
Numéro extrait : "123"
Champ trouvé : field_numero_de_text = "loi n° 123 du 15 mars 2023"
Résultat : Correspondance partielle (Score: 0.8)
```

## Configuration Recommandée

### 1. Structure des Noms de Fichiers PDF

**Format recommandé :**
- `[type]-[numero]-[annee]_[langue].pdf`
- Exemples : `loi-31.13-2013_ar.pdf`, `decret-2.21.968-2021_fr.pdf`

### 2. Remplissage du Champ `field_numero_de_text`

**Bonnes pratiques :**
- Utiliser le numéro de référence officiel exact
- Format cohérent : `31.13`, `1.18.15`, etc.
- Éviter les espaces et caractères spéciaux inutiles

### 3. Gestion des Erreurs 413

**Solutions intégrées :**
- Limitation automatique du nombre de fichiers par lot
- Détection des limites serveur
- Messages d'aide contextuels
- Script de configuration serveur fourni

## Logs et Débogage

**Nouveaux logs disponibles :**
- Extraction des numéros de référence
- Scores de correspondance détaillés
- Tentatives de recherche multi-langues
- Correspondances trouvées avec justification

**Consultation des logs :**
```bash
# Logs du module
tail -f web/sites/default/files/logs/import_reglementation.log

# Logs Drupal généraux
vendor/bin/drush watchdog:show --filter=import_reglementation
```

## Tests et Validation

**Script de test fourni :**
```bash
php test_module.php
```

**Vérifications effectuées :**
- Chargement du service
- Extraction des numéros de référence
- Détection de langue
- Comptage des nœuds disponibles
- Test des correspondances

## Compatibilité

**Versions supportées :**
- Drupal 9.x et 10.x
- PHP 7.4+
- Modules requis : node, file, taxonomy

**Pas de breaking changes :** Les anciennes fonctionnalités restent compatibles.

## Performance

**Optimisations apportées :**
- Recherche exacte prioritaire (plus rapide)
- Limitation des résultats LIKE à 10 éléments
- Cache des correspondances dans les logs
- Évitement des recherches redondantes

## Support

**En cas de problème :**
1. Vérifier les logs du module
2. Utiliser le script de diagnostic : `/admin/content/import-pdf-diagnostic`
3. Consulter le guide de dépannage : `QUICK_FIX.md`
4. Tester avec le script : `test_module.php` 