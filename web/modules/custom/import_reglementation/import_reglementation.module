<?php

/**
 * @file
 * Module file for Import Réglementation.
 */

use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;
use Drupal\file\FileInterface;
use Drupal\Component\Utility\Random;
use Drupal\Core\Form\FormStateInterface;

/**
 * Implements hook_help().
 */
function import_reglementation_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.import_reglementation':
      $output = '';
      $output .= '<h3>' . t('Import Réglementation') . '</h3>';
      $output .= '<p>' . t('Ce module permet d\'importer des réglementations depuis un fichier CSV.') . '</p>';
      return $output;
  }
}

/**
 * Implements hook_file_presave().
 *
 * Renames uploaded PDF files to a short, random, safe name if they are new,
 * to prevent issues with long or complex filenames on the filesystem.
 */
function import_reglementation_file_presave(FileInterface $file) {
  // Act on new PDF files only.
  // We also check if the URI scheme is temporary, indicating a new upload not yet moved.
  if ($file->isNew() && $file->getMimeType() === 'application/pdf' && $file->getFileUri() && strpos($file->getFileUri(), 'temporary://') === 0) {
    $original_filename = $file->getFilename();

    // Generate a short, random, safe filename.
    $random_generator = new Random();
    $new_filename_base = $random_generator->name(16, TRUE); // Generate a 16-char random alphanumeric string.
    $extension = 'pdf'; // We already know it's a PDF.

    $new_safe_filename = $new_filename_base . '.' . $extension;

    // Log the change for debugging and record-keeping.
    \Drupal::logger('import_reglementation')->notice(
      'Managed file upload: Original filename "@original" is being renamed to "@new" before saving to disk to prevent filesystem errors. The original name should still be displayed in the UI.',
      ['@original' => $original_filename, '@new' => $new_safe_filename]
    );

    // Set the new filename. Drupal will use this when moving the file from temporary to permanent storage.
    $file->setFilename($new_safe_filename);
  }
}

/**
 * Implements hook_form_alter().
 */
function import_reglementation_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Augmenter les limites pour notre formulaire d'import
  if ($form_id === 'import_pdf_zip_form') {
    // Augmenter les limites de mémoire et de temps d'exécution
    if (function_exists('ini_set')) {
      // Augmenter la limite de mémoire si possible
      $current_memory = ini_get('memory_limit');
      if ($current_memory && $current_memory !== '-1') {
        $memory_in_bytes = _import_reglementation_parse_size($current_memory);
        $min_memory = 256 * 1024 * 1024; // 256MB minimum
        if ($memory_in_bytes < $min_memory) {
          @ini_set('memory_limit', '256M');
        }
      }
      
      // Augmenter le temps d'exécution
      @ini_set('max_execution_time', 300); // 5 minutes
      
      // Augmenter les limites d'upload si possible
      @ini_set('upload_max_filesize', '100M');
      @ini_set('post_max_size', '500M');
      @ini_set('max_file_uploads', 20);
    }
  }
}

/**
 * Implements hook_file_validate().
 */
function import_reglementation_file_validate($file) {
  $errors = [];
  
  // Permettre des fichiers plus volumineux pour les PDFs de réglementation
  if ($file->getMimeType() === 'application/pdf') {
    // Vérifier si c'est dans le contexte de notre module
    $request = \Drupal::request();
    $route_name = \Drupal::routeMatch()->getRouteName();
    
    if ($route_name === 'import_reglementation.import_pdf' || 
        strpos($request->getRequestUri(), 'import-pdf') !== false) {
      
      // AUGMENTATION : Permettre jusqu'à 100MB pour les PDFs de réglementation
      $max_size = 100 * 1024 * 1024; // 100MB
      if ($file->getSize() > $max_size) {
        $errors[] = t('Le fichier @filename est trop volumineux (@size). La taille maximale autorisée est de @max.', [
          '@filename' => $file->getFilename(),
          '@size' => \Drupal::service('file.size')->formatBytes($file->getSize()),
          '@max' => \Drupal::service('file.size')->formatBytes($max_size),
        ]);
      }
      
      // Log pour debugging
      \Drupal::logger('import_reglementation')->info(
        'Validation fichier PDF: @filename (@size) - Max autorisé: @max',
        [
          '@filename' => $file->getFilename(),
          '@size' => \Drupal::service('file.size')->formatBytes($file->getSize()),
          '@max' => \Drupal::service('file.size')->formatBytes($max_size),
        ]
      );
    }
  }
  
  return $errors;
}

/**
 * Parse une taille de fichier depuis la configuration PHP.
 *
 * @param string $size
 *   Taille au format PHP (ex: "8M", "2G").
 *
 * @return int
 *   Taille en octets.
 */
function _import_reglementation_parse_size($size) {
  $size = trim($size);
  if (empty($size)) {
    return 0;
  }
  
  $last = strtolower($size[strlen($size) - 1]);
  $size = (int) $size;
  
  switch ($last) {
    case 'g':
      $size *= 1024;
    case 'm':
      $size *= 1024;
    case 'k':
      $size *= 1024;
  }
  
  return $size;
}