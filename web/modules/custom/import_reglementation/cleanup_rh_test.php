<?php

/**
 * Script de nettoyage pour supprimer les données de test RH
 * 
 * Usage: vendor/bin/drush scr web/modules/custom/import_reglementation/cleanup_rh_test.php
 */

echo "=== NETTOYAGE DES DONNÉES DE TEST RH ===\n";

// Rechercher tous les nœuds de réglementation créés récemment
$query = \Drupal::entityQuery('node')
  ->accessCheck(FALSE)
  ->condition('type', 'reglementation');

$nids = $query->execute();

if (empty($nids)) {
  echo "Aucun nœud de réglementation trouvé.\n";
  return;
}

echo "Nœuds de réglementation trouvés: " . count($nids) . "\n";

// Afficher les nœuds pour confirmation
$storage = \Drupal::entityTypeManager()->getStorage('node');
$nodes = $storage->loadMultiple($nids);

echo "\nListe des nœuds:\n";
foreach ($nodes as $node) {
  $numero = $node->hasField('field_numero_de_text') ? $node->get('field_numero_de_text')->value : 'N/A';
  echo "- ID: " . $node->id() . ", Titre: " . $node->getTitle() . ", Numéro: $numero\n";
}

// Demander confirmation (simulée pour le script)
echo "\n⚠️  ATTENTION: Ce script va supprimer TOUS les nœuds de réglementation.\n";
echo "Pour confirmer, modifiez le script et décommentez la ligne de suppression.\n";

// Ligne de suppression commentée pour sécurité
// $storage->delete($nodes);
// echo "\n✅ " . count($nodes) . " nœuds supprimés.\n";

echo "\n=== SCRIPT TERMINÉ ===\n";
