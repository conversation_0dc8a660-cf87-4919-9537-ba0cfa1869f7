#!/bin/bash

# Script de configuration pour l'import de PDF arabes
# Ce script aide à configurer les paramètres serveur pour éviter l'erreur 413

echo "=== Configuration serveur pour import PDF ==="
echo ""

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[ATTENTION]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERREUR]${NC} $1"
}

# Vérifier si on est root
if [[ $EUID -eq 0 ]]; then
   print_warning "Ce script est exécuté en tant que root. Assurez-vous que c'est intentionnel."
fi

# Détecter le système
if [ -f /etc/debian_version ]; then
    DISTRO="debian"
    print_status "Système Debian/Ubuntu détecté"
elif [ -f /etc/redhat-release ]; then
    DISTRO="redhat"
    print_status "Système RedHat/CentOS détecté"
else
    print_warning "Distribution non reconnue. Adaptation manuelle nécessaire."
    DISTRO="unknown"
fi

# Trouver le fichier php.ini
print_status "Recherche du fichier php.ini..."
PHP_INI=$(php --ini | grep "Loaded Configuration File" | cut -d: -f2 | xargs)

if [ -z "$PHP_INI" ] || [ ! -f "$PHP_INI" ]; then
    print_error "Fichier php.ini non trouvé. Vérifiez votre installation PHP."
    exit 1
fi

print_status "Fichier php.ini trouvé: $PHP_INI"

# Sauvegarder le fichier php.ini
BACKUP_FILE="${PHP_INI}.backup.$(date +%Y%m%d_%H%M%S)"
print_status "Sauvegarde de php.ini vers: $BACKUP_FILE"
cp "$PHP_INI" "$BACKUP_FILE"

# Fonction pour mettre à jour php.ini
update_php_ini() {
    local setting=$1
    local value=$2
    
    if grep -q "^${setting}" "$PHP_INI"; then
        # Le paramètre existe, le modifier
        sed -i "s/^${setting}.*/${setting} = ${value}/" "$PHP_INI"
        print_status "Mis à jour: ${setting} = ${value}"
    elif grep -q "^;${setting}" "$PHP_INI"; then
        # Le paramètre existe mais est commenté
        sed -i "s/^;${setting}.*/${setting} = ${value}/" "$PHP_INI"
        print_status "Activé et mis à jour: ${setting} = ${value}"
    else
        # Le paramètre n'existe pas, l'ajouter
        echo "${setting} = ${value}" >> "$PHP_INI"
        print_status "Ajouté: ${setting} = ${value}"
    fi
}

# Mettre à jour les paramètres PHP
print_status "Mise à jour des paramètres PHP..."

update_php_ini "upload_max_filesize" "100M"
update_php_ini "post_max_size" "120M"
update_php_ini "memory_limit" "256M"
update_php_ini "max_execution_time" "300"
update_php_ini "max_file_uploads" "20"
update_php_ini "max_input_vars" "3000"

# Configuration Nginx si présent
if command -v nginx &> /dev/null; then
    print_status "Nginx détecté. Configuration recommandée:"
    echo ""
    echo "Ajoutez ces lignes dans votre bloc server {} :"
    echo "    client_max_body_size 100M;"
    echo "    client_body_timeout 300s;"
    echo "    client_body_buffer_size 128k;"
    echo ""
    print_warning "Vous devez redémarrer Nginx après modification."
fi

# Configuration Apache si présent
if command -v apache2 &> /dev/null || command -v httpd &> /dev/null; then
    print_status "Apache détecté. Configuration recommandée:"
    echo ""
    echo "Ajoutez ces lignes dans votre configuration Apache ou .htaccess :"
    echo "    LimitRequestBody 104857600"
    echo "    TimeOut 300"
    echo ""
    print_warning "Vous devez redémarrer Apache après modification."
fi

# Redémarrage des services
print_status "Redémarrage des services PHP..."

if [ "$DISTRO" = "debian" ]; then
    # Détecter la version PHP
    PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
    
    if systemctl is-active --quiet php${PHP_VERSION}-fpm; then
        systemctl restart php${PHP_VERSION}-fpm
        print_status "PHP-FPM redémarré"
    elif systemctl is-active --quiet apache2; then
        systemctl restart apache2
        print_status "Apache redémarré"
    fi
    
elif [ "$DISTRO" = "redhat" ]; then
    if systemctl is-active --quiet php-fpm; then
        systemctl restart php-fpm
        print_status "PHP-FPM redémarré"
    elif systemctl is-active --quiet httpd; then
        systemctl restart httpd
        print_status "Apache redémarré"
    fi
fi

# Vérification finale
print_status "Vérification des nouveaux paramètres..."
echo ""
echo "upload_max_filesize: $(php -r 'echo ini_get("upload_max_filesize");')"
echo "post_max_size: $(php -r 'echo ini_get("post_max_size");')"
echo "memory_limit: $(php -r 'echo ini_get("memory_limit");')"
echo "max_execution_time: $(php -r 'echo ini_get("max_execution_time");')"
echo "max_file_uploads: $(php -r 'echo ini_get("max_file_uploads");')"

echo ""
print_status "Configuration terminée !"
print_warning "N'oubliez pas de :"
echo "  1. Redémarrer votre serveur web (Nginx/Apache) si nécessaire"
echo "  2. Tester l'import avec de petits lots de fichiers"
echo "  3. Consulter la page de diagnostic: /admin/content/import-pdf-diagnostic"
echo ""
print_status "Sauvegarde de l'ancien php.ini: $BACKUP_FILE" 