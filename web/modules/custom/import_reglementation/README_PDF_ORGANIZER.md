# Service PdfOrganizer

Le service `PdfOrganizer` permet d'organiser automatiquement les fichiers PDF selon une structure hiérarchique basée sur :
- La langue (Fr/AR)
- Le type de réglementation (Arrêté, Loi, Décret, etc.)
- Le numéro de texte

## Structure d'organisation

Les fichiers PDF sont organisés selon cette structure :
```
pdf/
├── Fr/
│   ├── Arrêté/
│   │   ├── Arrêté 20.80 Fr.pdf
│   │   └── Arrêté 1196.03 Fr.pdf
│   ├── Loi/
│   │   ├── Loi 116.14 Fr.pdf
│   │   └── Loi 52.05 Fr.pdf
│   └── Décret/
│       └── Décret 2.10.311 Fr.pdf
└── AR/
    ├── Arrêté/
    │   ├── Arrêté 20.80 Ar.pdf
    │   └── Arrêté 1196.03 Ar.pdf
    └── Loi/
        └── Loi 116.14 Ar.pdf
```

## Analyse des noms de fichiers

Le service peut analyser différents formats de noms de fichiers :

### Formats supportés

1. **Format standard** : `Type Numéro Langue.pdf`
   - Exemple : `Arrêté 20.80 Fr.pdf`
   - Exemple : `Loi 116.14 Ar.pdf`

2. **Format avec underscores** : `Type_Numéro_Langue.pdf`
   - Exemple : `Loi_52.05_Fr.pdf`
   - Exemple : `Décret_2.10.311_Ar.pdf`

3. **Format sans langue explicite** : `Type Numéro.pdf`
   - Exemple : `Arrêté 20.80.pdf` (langue déduite du contexte)

### Types de réglementation supportés

- Arrêté
- Loi
- Décret
- Dahir
- Circulaire
- Décision
- Cahiers des charges / Cahier des charges
- CDC (abréviation pour Cahiers des charges)

### Langues supportées

- **Fr** : Français
- **AR** : Arabe

## Utilisation du service

### Via le code PHP

```php
// Obtenir le service
$pdf_organizer = \Drupal::service('import_reglementation.pdf_organizer');

// Analyser un nom de fichier
$result = $pdf_organizer->analyzeFilename('Arrêté 20.80 Fr.pdf');
if ($result['valid']) {
  echo "Type: {$result['type']}";
  echo "Numéro: {$result['numero']}";
  echo "Langue: {$result['langue']}";
}

// Organiser un fichier (copier)
$result = $pdf_organizer->organizeFile('/path/to/file.pdf', false);

// Organiser un fichier (déplacer)
$result = $pdf_organizer->organizeFile('/path/to/file.pdf', true);

// Organiser tout un dossier
$results = $pdf_organizer->organizeDirectory('/path/to/source/directory', false);

// Rechercher un fichier par numéro et type
$files = $pdf_organizer->findFileByNumber('20.80', 'Arrêté', 'Fr');
```

### Via les commandes Drush

#### Analyser un nom de fichier
```bash
vendor/bin/drush pdf:analyze "Arrêté 20.80 Fr.pdf"
```

#### Organiser un fichier
```bash
# Copier le fichier
vendor/bin/drush pdf:organize-file /path/to/file.pdf

# Déplacer le fichier
vendor/bin/drush pdf:organize-file /path/to/file.pdf --move
```

#### Organiser un dossier complet
```bash
# Copier tous les PDF du dossier
vendor/bin/drush pdf:organize-directory /path/to/source/directory

# Déplacer tous les PDF du dossier et sous-dossiers
vendor/bin/drush pdf:organize-directory /path/to/source/directory --move --recursive
```

#### Rechercher des fichiers
```bash
# Rechercher dans toutes les langues
vendor/bin/drush pdf:find "20.80" "Arrêté"

# Rechercher dans une langue spécifique
vendor/bin/drush pdf:find "116.14" "Loi" --langue=Fr
```

#### Afficher la structure des dossiers
```bash
vendor/bin/drush pdf:structure
```

## Méthodes du service

### `analyzeFilename($filename)`
Analyse un nom de fichier pour extraire les informations.

**Paramètres :**
- `$filename` : Nom du fichier (avec ou sans extension)

**Retour :**
```php
[
  'type' => 'Arrêté',           // Type de réglementation
  'numero' => '20.80',          // Numéro de texte
  'langue' => 'Fr',             // Langue
  'valid' => true,              // Succès de l'analyse
  'original_filename' => '...'  // Nom de fichier original
]
```

### `organizeFile($source_file_path, $move = false)`
Organise un fichier dans la structure appropriée.

**Paramètres :**
- `$source_file_path` : Chemin complet vers le fichier source
- `$move` : TRUE pour déplacer, FALSE pour copier

**Retour :**
```php
[
  'success' => true,
  'message' => 'Fichier copié avec succès...',
  'destination_path' => '/path/to/destination',
  'file_info' => [...] // Informations analysées
]
```

### `organizeDirectory($source_directory, $move = false, $recursive = false)`
Organise tous les fichiers PDF d'un dossier.

**Paramètres :**
- `$source_directory` : Dossier source
- `$move` : TRUE pour déplacer, FALSE pour copier
- `$recursive` : TRUE pour traiter les sous-dossiers

**Retour :**
```php
[
  'success' => 5,     // Nombre de succès
  'errors' => 1,      // Nombre d'erreurs
  'total' => 6,       // Total traité
  'details' => [...]  // Détails par fichier
]
```

### `findFileByNumber($numero, $type, $langue = '')`
Recherche des fichiers par numéro et type.

**Paramètres :**
- `$numero` : Numéro de texte
- `$type` : Type de réglementation
- `$langue` : Langue (optionnel)

**Retour :**
```php
[
  [
    'path' => '/full/path/to/file.pdf',
    'filename' => 'Arrêté 20.80 Fr.pdf',
    'info' => [...] // Informations analysées
  ]
]
```

## Test du service

Un script de test est disponible pour vérifier le fonctionnement :

```bash
cd /var/www/html/mtl
php web/modules/custom/import_reglementation/test_pdf_organizer.php
```

Ce script teste :
- L'analyse de différents formats de noms de fichiers
- La recherche de fichiers existants
- La simulation d'organisation de fichiers
- L'affichage de la structure actuelle

## Exemples d'utilisation

### Exemple 1 : Analyser un fichier
```php
$pdf_organizer = \Drupal::service('import_reglementation.pdf_organizer');
$result = $pdf_organizer->analyzeFilename('Arrêté 20.80 Fr.pdf');

// Résultat :
// [
//   'type' => 'Arrêté',
//   'numero' => '20.80',
//   'langue' => 'Fr',
//   'valid' => true,
//   'original_filename' => 'Arrêté 20.80 Fr.pdf'
// ]
```

### Exemple 2 : Organiser un fichier
```php
$result = $pdf_organizer->organizeFile('/tmp/Loi_116.14_Fr.pdf', false);

// Le fichier sera copié vers :
// pdf/Fr/Loi/Loi_116.14_Fr.pdf
```

### Exemple 3 : Rechercher des fichiers
```php
$files = $pdf_organizer->findFileByNumber('20.80', 'Arrêté');

// Retourne tous les fichiers "Arrêté 20.80" dans toutes les langues
```

## Notes importantes

1. **Sécurité** : Le service valide les chemins et noms de fichiers pour éviter les problèmes de sécurité.

2. **Création automatique** : Les dossiers de destination sont créés automatiquement si nécessaire.

3. **Gestion des erreurs** : Toutes les opérations sont loggées et les erreurs sont gérées proprement.

4. **Performance** : Le service utilise des méthodes optimisées pour traiter de gros volumes de fichiers.

5. **Flexibilité** : Le service peut être étendu pour supporter de nouveaux types de réglementation ou formats de noms de fichiers.
