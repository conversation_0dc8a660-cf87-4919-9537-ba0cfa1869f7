<?php

/**
 * @file
 * Module MTL Content Filter.
 */

use Dr<PERSON>al\views\ViewExecutable;
use Drupal\views\Plugin\views\query\QueryPluginBase;

/**
 * Implements hook_views_query_alter().
 */
function mtl_content_filter_views_query_alter(ViewExecutable $view, QueryPluginBase $query) {
  // Ne s'applique que sur la vue 'content'
  if ($view->id() != 'content') {
    return;
  }

  $current_user = \Drupal::currentUser();
  
  // Définir les mappings rôle => types de contenu
  $role_content_mapping = [
    'direction_des_systemes_d_information' => ['e_service'],
    'direction_des_affaires_administratives_juridiques_et_generales' => ['carrieres', 'reglementation'],
    'direction_de_la_strategie_du_pilotage_et_de_la_coordination_des' => ['chiffres_cles', 'organisme_sous_tutelle'],
    'division_de_la_cooperation_et_de_la_communication_secretariat_ge' => ['actualite', 'agenda', 'communique_de_presse', 'mediatheque', 'publication', 'faq', 'lien_utile'],
  ];

  // Vérifier si l'utilisateur a un des rôles configurés
  foreach ($role_content_mapping as $role => $content_types) {
    if ($current_user->hasRole($role)) {
      // Filtrer pour ne montrer que les types de contenu autorisés pour ce rôle
      $query->addWhere('content_type_filter', 'node_field_data.type', $content_types, 'IN');
      break; // Sortir de la boucle dès qu'un rôle correspond
    }
  }
}

/**
 * Implements hook_views_pre_view().
 */
function mtl_content_filter_views_pre_view(ViewExecutable $view, $display_id, array &$args) {
  // Ne s'applique que sur la vue 'content'
  if ($view->id() != 'content') {
    return;
  }

  $current_user = \Drupal::currentUser();
  
  // Définir les mappings rôle => types de contenu et titres
  $role_config = [
    'direction_des_systemes_d_information' => [
      'content_types' => ['e_service'],
      'title' => 'Contenu - E-Services'
    ],
    'direction_des_affaires_administratives_juridiques_et_generales' => [
      'content_types' => ['carrieres', 'reglementation'],
      'title' => 'Contenu - Carrières & Réglementations'
    ],
    'direction_de_la_strategie_du_pilotage_et_de_la_coordination_des' => [
      'content_types' => ['chiffres_cles', 'organisme_sous_tutelle'],
      'title' => 'Contenu - Stratégie & Pilotage'
    ],
    'division_de_la_cooperation_et_de_la_communication_secretariat_ge' => [
      'content_types' => ['actualite', 'agenda', 'communique_de_presse', 'mediatheque', 'publication', 'faq', 'lien_utile'],
      'title' => 'Contenu - Communication & Coopération'
    ],
  ];

  // Vérifier si l'utilisateur a un des rôles configurés
  foreach ($role_config as $role => $config) {
    if ($current_user->hasRole($role)) {
      // Modifier le titre de la vue pour indiquer qu'elle est filtrée
      $view->setTitle($config['title']);
      
      // Modifier le filtre de type pour ne montrer que les types autorisés
      if (isset($view->filter['type'])) {
        $allowed_types = array_combine($config['content_types'], $config['content_types']);
        $view->filter['type']->value = $allowed_types;
      }
      break; // Sortir de la boucle dès qu'un rôle correspond
    }
  }
} 