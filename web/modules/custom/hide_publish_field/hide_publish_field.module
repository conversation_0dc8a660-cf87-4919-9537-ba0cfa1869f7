<?php

/**
 * @file
 * Module pour masquer le champ Published pour le rôle contributeur.
 */

use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Access\AccessResult;

/**
 * Implements hook_form_alter().
 */
function hide_publish_field_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Vérifier si c'est un formulaire de création/édition de contenu
  if (strpos($form_id, 'node_') === 0 && (strpos($form_id, '_form') !== FALSE || strpos($form_id, '_edit_form') !== FALSE)) {
    
    $current_user = \Drupal::currentUser();
    
    // Si l'utilisateur a le rôle contributeur
    if (in_array('contributeur', $current_user->getRoles())) {
      
      // Masquer le champ Published
      if (isset($form['status'])) {
        $form['status']['#access'] = FALSE;
        
        // Forcer le statut à non publié (0) pour les contributeurs
        $form['status']['widget']['value']['#default_value'] = 0;
      }
      
      // Masquer aussi le champ Sticky (épinglé) s'il existe
      if (isset($form['sticky'])) {
        $form['sticky']['#access'] = FALSE;
      }
      
      // Masquer le champ Promote (promu en page d'accueil) s'il existe
      if (isset($form['promote'])) {
        $form['promote']['#access'] = FALSE;
      }
    }
  }
}

/**
 * Implements hook_entity_presave().
 */
function hide_publish_field_entity_presave($entity) {
  // Si c'est un node et que l'utilisateur actuel est contributeur
  if ($entity->getEntityTypeId() === 'node') {
    $current_user = \Drupal::currentUser();
    
    if (in_array('contributeur', $current_user->getRoles())) {
      // Forcer le statut à non publié pour les contributeurs
      $entity->setUnpublished();
    }
  }
} 