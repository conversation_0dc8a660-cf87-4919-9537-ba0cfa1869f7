<?php

/**
 * @file
 * Install, update and uninstall functions for the scraper_news module.
 */

/**
 * Implements hook_schema().
 */
function scraper_news_schema() {
  $schema['scraper_news_last_id'] = [
    'description' => 'Stores the last scraped ID for each language.',
    'fields' => [
      'id' => [
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
      ],
      'language' => [
        'type' => 'varchar',
        'length' => 10,
        'not null' => TRUE,
        'default' => '',
      ],
      'last_id' => [
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'default' => 0,
      ],
      'timestamp' => [
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
      ],
    ],
    'primary key' => ['id'],
    'indexes' => [
      'language' => ['language'],
    ],
  ];

  return $schema;
}