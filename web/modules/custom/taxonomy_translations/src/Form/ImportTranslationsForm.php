<?php

namespace Drupal\taxonomy_translations\Form;

use Dr<PERSON>al\Core\Form\FormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use Drupal\taxonomy\Entity\Term;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Formulaire pour importer des traductions de taxonomie.
 */
class ImportTranslationsForm extends FormBase {

  /**
   * Le service de système de fichiers.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * Constructeur.
   *
   * @param \Drupal\Core\File\FileSystemInterface $file_system
   *   Le service de système de fichiers.
   */
  public function __construct(FileSystemInterface $file_system) {
    $this->fileSystem = $file_system;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('file_system')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'taxonomy_translations_import_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    // Liste des vocabulaires disponibles
    $vocabularies = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_vocabulary')
      ->loadMultiple();
    
    $vocabulary_options = [];
    foreach ($vocabularies as $vid => $vocabulary) {
      $vocabulary_options[$vid] = $vocabulary->label();
    }
    
    $form['vocabulary'] = [
      '#type' => 'select',
      '#title' => $this->t('Vocabulaire'),
      '#description' => $this->t('Sélectionnez le vocabulaire pour lequel vous souhaitez importer des traductions.'),
      '#options' => $vocabulary_options,
      '#required' => TRUE,
    ];
    
    $form['csv_file'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Fichier CSV'),
      '#description' => $this->t('Sélectionnez le fichier CSV contenant les traductions. Le fichier doit contenir au moins deux colonnes: une pour les termes en français et une pour les termes en arabe.'),
      '#upload_location' => 'public://imports/',
      '#required' => TRUE,
      '#upload_validators' => [
        'FileExtension' => ['extensions' => 'csv'],
      ],
    ];
    
    $form['delimiter'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Délimiteur'),
      '#default_value' => ',',
      '#required' => TRUE,
      '#size' => 2,
      '#maxlength' => 1,
      '#description' => $this->t('Le caractère utilisé pour séparer les colonnes (généralement "," ou ";").'),
    ];
    
    $form['fr_column'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Colonne français'),
      '#default_value' => 'fr',
      '#required' => TRUE,
      '#description' => $this->t('Le nom de la colonne contenant les termes en français.'),
    ];
    
    $form['ar_column'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Colonne arabe'),
      '#default_value' => 'ar',
      '#required' => TRUE,
      '#description' => $this->t('Le nom de la colonne contenant les termes en arabe.'),
    ];
    
    $form['actions'] = [
      '#type' => 'actions',
    ];
    
    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Importer'),
    ];
    
    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $vocabulary = $form_state->getValue('vocabulary');
    $file = $form_state->getValue('csv_file');
    $delimiter = $form_state->getValue('delimiter');
    $fr_column = $form_state->getValue('fr_column');
    $ar_column = $form_state->getValue('ar_column');
    
    if (!empty($file)) {
      $file = \Drupal::entityTypeManager()->getStorage('file')->load($file[0]);
      $uri = $file->getFileUri();
      $file_path = $this->fileSystem->realpath($uri);
      
      $result = $this->importTranslations($file_path, $vocabulary, $delimiter, $fr_column, $ar_column);
      
      if ($result['success']) {
        $this->messenger()->addStatus($this->t('Import terminé avec succès. @count traductions ajoutées.', [
          '@count' => $result['count'],
        ]));
      }
      else {
        $this->messenger()->addError($this->t('Une erreur est survenue lors de l\'import.'));
        foreach ($result['errors'] as $error) {
          $this->messenger()->addError($error);
        }
      }
    }
  }
  
  /**
   * Importe les traductions depuis un fichier CSV.
   *
   * @param string $file_path
   *   Chemin vers le fichier CSV.
   * @param string $vocabulary
   *   ID du vocabulaire.
   * @param string $delimiter
   *   Délimiteur utilisé dans le CSV.
   * @param string $fr_column
   *   Nom de la colonne contenant les termes en français.
   * @param string $ar_column
   *   Nom de la colonne contenant les termes en arabe.
   *
   * @return array
   *   Résultats de l'import.
   */
  protected function importTranslations($file_path, $vocabulary, $delimiter, $fr_column, $ar_column) {
    $results = [
      'success' => FALSE,
      'count' => 0,
      'errors' => [],
    ];
    
    if (!file_exists($file_path)) {
      $results['errors'][] = $this->t('Le fichier CSV n\'existe pas.');
      return $results;
    }
    
    if (($handle = fopen($file_path, 'r')) !== FALSE) {
      // Lire l'en-tête pour obtenir les noms de colonnes
      $header = fgetcsv($handle, 0, $delimiter);
      
      // Vérifier que les colonnes requises sont présentes
      if (!in_array($fr_column, $header) || !in_array($ar_column, $header)) {
        $results['errors'][] = $this->t('Les colonnes @fr_column et/ou @ar_column sont manquantes dans le fichier CSV.', [
          '@fr_column' => $fr_column,
          '@ar_column' => $ar_column,
        ]);
        fclose($handle);
        return $results;
      }
      
      // Traiter chaque ligne du CSV
      while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
        $row = array_combine($header, $data);
        
        if (empty($row[$fr_column]) || empty($row[$ar_column])) {
          continue;
        }
        
        try {
          // Rechercher le terme en français
          $terms = \Drupal::entityTypeManager()
            ->getStorage('taxonomy_term')
            ->loadByProperties([
              'name' => $row[$fr_column],
              'vid' => $vocabulary,
              'langcode' => 'fr',
            ]);
          
          if (empty($terms)) {
            continue;
          }
          
          $term = reset($terms);
          
          // Vérifier si le terme a déjà une traduction en arabe
          if ($term->hasTranslation('ar')) {
            $translation = $term->getTranslation('ar');
          } 
          else {
            // Créer une nouvelle traduction
            $translation = $term->addTranslation('ar');
          }
          
          // Définir le nom en arabe
          $translation->setName($row[$ar_column]);
          
          // Sauvegarder la traduction
          $translation->save();
          
          $results['count']++;
        }
        catch (\Exception $e) {
          $results['errors'][] = $this->t('Erreur lors du traitement de la ligne: @error', ['@error' => $e->getMessage()]);
        }
      }
      
      fclose($handle);
      $results['success'] = TRUE;
    }
    else {
      $results['errors'][] = $this->t('Impossible d\'ouvrir le fichier CSV.');
    }
    
    return $results;
  }
}
