<?php

/**
 * @file
 * Module pour corriger les valeurs null dans les données de vues.
 */

use <PERSON><PERSON><PERSON>\Component\Render\FormattableMarkup;

/**
 * Implements hook_views_data_alter().
 */
function views_data_fix_views_data_alter(array &$data) {
  // Parcourir toutes les tables de données de vues
  foreach ($data as $table_name => &$table_data) {
    // Parcourir tous les champs de chaque table
    foreach ($table_data as $field_name => &$field_data) {
      // Ignorer les données de table
      if ($field_name === 'table') {
        continue;
      }
      
      // Parcourir tous les types de handlers (field, filter, sort, etc.)
      foreach (['field', 'sort', 'filter', 'argument', 'relationship', 'area'] as $handler_type) {
        if (isset($field_data[$handler_type])) {
          // Corriger les valeurs null pour title et group
          if (!isset($field_data[$handler_type]['title']) || $field_data[$handler_type]['title'] === null) {
            $field_data[$handler_type]['title'] = new FormattableMarkup('Field: @field', ['@field' => $field_name]);
          }
          
          if (!isset($field_data[$handler_type]['group']) || $field_data[$handler_type]['group'] === null) {
            $field_data[$handler_type]['group'] = new FormattableMarkup('Table: @table', ['@table' => $table_name]);
          }
          
          // S'assurer que title et group sont des chaînes ou des objets FormattableMarkup
          if (is_string($field_data[$handler_type]['title']) && empty($field_data[$handler_type]['title'])) {
            $field_data[$handler_type]['title'] = new FormattableMarkup('Field: @field', ['@field' => $field_name]);
          }
          
          if (is_string($field_data[$handler_type]['group']) && empty($field_data[$handler_type]['group'])) {
            $field_data[$handler_type]['group'] = new FormattableMarkup('Table: @table', ['@table' => $table_name]);
          }
        }
      }
    }
  }
}

/**
 * Implements hook_views_pre_build().
 */
function views_data_fix_views_pre_build($view) {
  // Log pour le débogage si nécessaire
  if ($view->id() === 'search') {
    \Drupal::logger('views_data_fix')->debug('Vue search en cours de construction');
  }
} 