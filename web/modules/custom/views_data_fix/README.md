# Views Data Fix Module

## Description

Ce module personnalisé corrige l'erreur suivante qui se produit lors de l'ajout de champs dans les vues :

```
TypeError: Drupal\Component\Utility\Html::escape(): Argument #1 ($text) must be of type string, null given
```

## Problème

L'erreur se produit quand :
1. Des champs de vue ont des valeurs `null` pour les propriétés `title` ou `group`
2. Ces valeurs null sont passées à travers la chaîne d'appels jusqu'à `Html::escape()`
3. La fonction `Html::escape()` ne peut pas accepter de valeurs null (seulement des chaînes)

## Solution

Le module implémente deux corrections :

### 1. `hook_views_data_alter()`
- Parcourt toutes les données de vues
- Remplace les valeurs `null` ou vides pour `title` et `group` par des objets `FormattableMarkup` appropriés
- Assure que tous les champs ont des valeurs valides

### 2. Event Subscriber
- Intercepte les exceptions de type `TypeError` liées à `Html::escape()`
- Enregistre les erreurs dans les logs pour le débogage
- Permet une gestion gracieuse des erreurs

## Installation

Le module est automatiquement activé et ne nécessite aucune configuration supplémentaire.

## Utilisation

Une fois activé, le module :
- Corrige automatiquement les données de vues problématiques
- Empêche l'erreur `Html::escape()` de se produire
- Permet l'ajout de champs dans les vues sans erreur

## Débogage

Les logs sont disponibles dans :
- Channel : `views_data_fix`
- Niveau : DEBUG/ERROR selon le type d'événement

## Compatibilité

- Drupal 10.x
- Drupal 11.x 