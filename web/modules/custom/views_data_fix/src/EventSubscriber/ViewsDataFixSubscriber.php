<?php

namespace Drupal\views_data_fix\EventSubscriber;

use <PERSON><PERSON><PERSON>\Core\Logger\LoggerChannelFactoryInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Event subscriber pour gérer les erreurs de vues.
 */
class ViewsDataFixSubscriber implements EventSubscriberInterface {

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * Constructs a new ViewsDataFixSubscriber.
   *
   * @param \Drupal\Core\Logger\LoggerChannelFactoryInterface $logger_factory
   *   The logger factory.
   */
  public function __construct(LoggerChannelFactoryInterface $logger_factory) {
    $this->loggerFactory = $logger_factory;
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents() {
    return [
      KernelEvents::EXCEPTION => ['onException', 50],
    ];
  }

  /**
   * Handles exceptions.
   *
   * @param \Symfony\Component\HttpKernel\Event\ExceptionEvent $event
   *   The exception event.
   */
  public function onException(ExceptionEvent $event) {
    $exception = $event->getThrowable();
    
    // Vérifier si c'est l'erreur spécifique que nous voulons gérer
    if ($exception instanceof \TypeError &&
        strpos($exception->getMessage(), 'Html::escape()') !== FALSE &&
        strpos($exception->getMessage(), 'must be of type string, null given') !== FALSE) {
      
      $this->loggerFactory->get('views_data_fix')->error(
        'Erreur Html::escape() détectée et gérée: @message. Trace: @trace',
        [
          '@message' => $exception->getMessage(),
          '@trace' => $exception->getTraceAsString(),
        ]
      );
      
      // Optionnel : rediriger vers une page d'erreur personnalisée
      // ou essayer de récupérer gracieusement
    }
  }
} 