"use strict";

(function ($) {
  var app;
  app = {
    init: function init() {
      this._readyGlobal();
      this._resizeGlobal();
      this._loadGlobal();
      this._menu();
      this._search();
      this._addscroll();
      this._swiper();
      this._accordion();
      this._tabs();
      this._video();
      this._select();
      this._validationsJs();
      //  AOS.init();
    },

    //_readyGlobal
    _readyGlobal: function _readyGlobal() {
      $('#accessPanel').removeAttr( 'style' );
      // Go to top
      document.addEventListener('DOMContentLoaded', function () {
        const goToTop = document.querySelector('.goTop');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                goToTop.classList.add('is-visible');
            } else {
                goToTop.classList.remove('is-visible');
            }
        });
    
        goToTop.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        let $header = $('.header');
        if ($header.length > 0) {
          let $paddingTop = $('body.toolbar-fixed').css('padding-top');
          $header.css('top', $paddingTop);
        }
        if (document.querySelector('.tabs-button')) {
          document.body.classList.add('has-tabs-button');
        }
        if (document.querySelector('.e-services-wysiw') || document.querySelector('.accord-wysiw') ) {
          document.body.classList.add('page-content');
        }
        if (document.querySelector('.not-found')) {
          document.body.classList.add('page-404');
        } 
        if (document.querySelector('form.user-login-form')) {
          document.body.classList.add('page-log');
        }
        const wysiwyg = document.querySelector('.bannerHp__page-interne + div > .m-organigramme > .container > #c-wysiwyg');
        if (wysiwyg && wysiwyg.querySelector('.organigrame')) {
          document.body.classList.add('body-org');
        }
        const scrollWrapper = document.querySelector('#tab3 > .paragraph--type--organigrame');
        if (scrollWrapper && scrollWrapper.querySelector('.scroll-wrapper')) {
          document.body.classList.add('marg-org');
        }
        //append social-media-sharing
        const ul = document.querySelector(".social-media-sharing ul");
        const a2aSpan = document.querySelector(".a2a_kit");

        if (ul && a2aSpan) {
          const lastLi = ul.querySelector("li:last-child");
          const newLi = document.createElement("li");
          newLi.appendChild(a2aSpan);
          ul.appendChild(newLi);
        }
        
      });
      
      
      // const sticky = new Sticky('.tabs-button');

      //AjaxComplet
      $(document).ajaxComplete(function () {
        //e-serices
        if ($(".swiper-services").length > 0) {
          var swiper = new Swiper(".swiper-services", {
              direction: 'horizontal',
              slidesPerView: 3,
              spaceBetween: 0,
              centeredSlides: true,
              loop: true,
              initialSlide: 1,
              navigation: {
                  nextEl: ".swiper-button-next",
                  prevEl: ".swiper-button-prev",
              },
              autoplay: {
                delay: 2000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              },
              speed: 2500,
              breakpoints: {
                  0: { slidesPerView: 1, speed: 2500,},
                  482: { slidesPerView: 2, initialSlide: 0, centeredSlides: false},
                  1024: { slidesPerView: 3 }
              }
          });
        }
        setTimeout(function() {
          var controller = new ScrollMagic.Controller();
          var revealElements = document.getElementsByClassName("digit");
          for (var i=0; i<revealElements.length; i++) {
              new ScrollMagic.Scene({
              triggerElement: revealElements[i],
              offset: 50,
              triggerHook: 0.9,
            })
            .setClassToggle(revealElements[i], "visible")
            .addTo(controller);
          }
          new ScrollMagic.Scene({
            triggerElement: "#trigger1",
            triggerHook: 0.9,
            duration: "0",
            offset: 50
          })
          .setClassToggle("#reveal1", "visible")
          .addTo(controller);
        },10)
        
      })
      document.addEventListener("DOMContentLoaded", function () {
        Fancybox.bind("[data-fancybox]", {
          Thumbs: {
            autoStart: false,
          },
          buttons: [
            "zoom",
            "slideShow",
            "thumbs",
            "close"
          ]
        });
      });
    },
    //resizeGlobal
    _resizeGlobal: function _resizeGlobal() {
      $(document).on('resize', function () {

      });
    },
    //loadGlobal
    _loadGlobal: function _loadGlobal() {
      // $(window).on('load', function () {
      //   const path = window.location.pathname;
      //   if (!path.match(/^\/(fr|en)(\/|$)/)) return;

      //   function wrapFirstLetters() {
      //     $('h1, h2, h3').each(function () {
      //       const $el = $(this);
        
      //       // Ignorer si déjà traité
      //       if ($el.find('#first-letter').length) return;
        
      //       // Ignorer si tout le contenu est un seul <a>
      //       const children = $el.children();
      //       if (children.length === 1 && children.is('a')) return;
        
      //       const textOnly = $el.text().trim();
      //       if (textOnly.length === 0) return;
        
      //       const firstChar = textOnly.charAt(0).toUpperCase();
      //       const restText = textOnly.slice(1);
        
      //       const newText = `<span id="first-letter">${firstChar}</span>${restText}`;
      //       $el.html(newText);
      //     });
      //   }

      //   wrapFirstLetters();

      //   const observer = new MutationObserver(wrapFirstLetters);
      //   observer.observe(document.body, { childList: true, subtree: true });

      // });
    },

    //Menu
    _menu: function _menu() {
      //form search menu
      $(document).ready(function () {
        //Decouvrir plus priority
        $('.share-buttons').insertBefore('.bloc-decouvrir');

        // $('form#views-exposed-form-projects-page-1 select').on('change', function () {
        //   $('form#views-exposed-form-projects-page-1 input[type="submit"]').prop('disabled', false);
        // });
        // $('form#views-exposed-form-projects-page-1 input[type="submit"]').on('click', function () {
        //  $('form#views-exposed-form-projects-page-1').submit();
        // });
        // $('form#views-exposed-form-projects-page-1 input[type="radio"]').on('change', function () {
        //   $('form#views-exposed-form-projects-page-1').submit();
        // });
        $('.search-icon').click(function () {
            if ($(window).width() > 992) { 
                $(this).toggleClass('search-icon__active');
                $(this).closest('.wrapper-menu-form').toggleClass('disable-menu');
            }
            const $overlay = $('.header__bottom--overlay');
            const $input = $overlay.find('input[type="text"]');
        
            if (!$overlay.is(':visible')) {
                $overlay.slideDown('fast', function () {
                    $input.val('').focus();
                });
            } else {
                $overlay.slideUp('fast');
            }
        });
        let toggle_label = $('.nav-toggle-label');
        $(toggle_label).on('click', function () {
            if (window.matchMedia("(max-width: 992px)").matches) {
                $('body,html').toggleClass('toggle');
            }
        });

        //reload
          let mediaQuery = window.matchMedia("(max-width: 992px)");
          let lastState = mediaQuery.matches ? 'mobile' : 'desktop';

          mediaQuery.addEventListener('change', function (event) {
            let newState = event.matches ? 'mobile' : 'desktop';
            if (newState !== lastState) {
              lastState = newState;
              location.reload();
            }
          });
            const headerBottomNav = document.querySelector('.header__bottom nav[role="navigation"]');
            const overlay = document.querySelector('.header__bottom--overlay');
            const langSwitcher = document.querySelector('.header__top--lang.language-switcher-language-url');
            const headerTopMenu = document.querySelector('.header__top--menu.animation-link');
            const headerTopRsociaux = document.querySelector('.header__top--rsociaux');
            const headerTopMobile = document.createElement('div');
            headerTopMobile.classList.add('header-bottom-mobile');

            if (window.innerWidth <= 992) {
              headerTopMobile.appendChild(headerTopMenu);
              headerTopMobile.appendChild(headerTopRsociaux);
              headerBottomNav.appendChild(headerTopMobile);

              const headerBotMobile = document.createElement('div');
              headerBotMobile.classList.add('header-top-mobile');
              headerBotMobile.appendChild(langSwitcher);
              headerBotMobile.appendChild(overlay);
              headerBottomNav.prepend(headerBotMobile);
            }
            
            //organigrame
            $(".quotes").closest(".container").addClass("wysi-quotes");

            // var maxHeight = 0;
  
            // $('.mtl-tv__items--item h3,.reglementation .card h2.title-reg, .partenaire .col-md-4 h3').each(function() {
            //   var thisHeight = $(this).height();
            //   if (thisHeight > maxHeight) {
            //     maxHeight = thisHeight;
            //   }
            // });

            // $('.mtl-tv__items--item h3, .reglementation .card h2.title-reg, .partenaire .col-md-4 h3').height(maxHeight);
            function uniformiserHauteursTitres() {
            var maxHeight = 0;
            var $titres = $('.mtl-tv__items--item h3, .reglementation .card h2.title-reg, .partenaire h3');

            // Réinitialiser les hauteurs pour ne pas cumuler
            $titres.css('height', 'auto');

            // Trouver la hauteur max
            $titres.each(function () {
            var thisHeight = $(this).height();
            if (thisHeight > maxHeight) {
            maxHeight = thisHeight;
            }
            });

            // Appliquer la hauteur max à tous
            $titres.height(maxHeight);
            }

            // Appel initial au chargement
            $(document).ready(function () {
              uniformiserHauteursTitres();
            });

            // Appel au resize (responsive)
            $(window).on('resize', function () {
              uniformiserHauteursTitres();
            });

            
            if ($('.organigrame__membres--two img').length > 0) {
              $('.tabs-content .organigrame').addClass('organisation');
            }

              $('.bloc-odd').each(function () {
                const $left = $(this).find('.presentation-left');
                const $right = $(this).find('.presentation-right');
                if ($right.index() < $left.index()) {
                  $(this).addClass('is-reversed');
                }
              });


              //tronquage 
              function truncateTitle() {
                var maxWords = 14;
                var titleElement = $('.breadCrumb ul li:last-child');
                var originalText = titleElement.data('original-text');
              
                if (!originalText) {
                  originalText = titleElement.text().trim();
                  titleElement.data('original-text', originalText);
                }
              
                var words = originalText.split(/\s+/);
              
                if (words.length > maxWords) {
                  var truncatedText = words.slice(0, maxWords).join(' ') + '...';
                  titleElement.text(truncatedText);
                } else {
                  titleElement.text(originalText);
                }
              }
              
              truncateTitle();
              $(window).on('resize', truncateTitle);
      });

      //Menu mobile
        function initMenuScripts() {
          if ($(window).width() <= 992) {
            $('.header__bottom nav ul > li:has(ul) > a').off('click').on('click', function(e) {
              e.preventDefault();
              var $this = $(this);
              var $submenu = $this.next('ul');
        
              $('.header__bottom .card .card-body > div').slideUp();
              $('.header__bottom .card .card-body > div').filter(function() {
                return $(this).text().trim() === "";
              }).closest('.card-body').addClass('card-body--toggle');
              
              if ($submenu.is(':visible')) {
                $submenu.slideUp();
                $this.removeClass('open');
                $('.header__bottom .card span.card-title').removeClass('open');
              } else {
                $('.header__bottom nav ul > li:has(ul) ul:visible').slideUp().prev('a').removeClass('open');
                $submenu.slideDown();
                $this.addClass('open');
              }
            });
            
            $('.header__bottom .card > div.card-body > span.card-title').off('click').on('click', function(e) {
              e.preventDefault();
              var $this = $(this);
              var $content = $this.next('div:has(*)');
        
              $('.header__bottom .card span.card-title.open').not($this).removeClass('open').next('div:visible').slideUp();
        
              $content.slideToggle(function() {
                $this.toggleClass('open', $content.is(':visible'));
              });
            });
          } else {
            $('.header__bottom nav ul > li:has(ul) > a').off('click');
            $('.header__bottom .card .span.card-title').off('click');
          }
        }
        // Exécuter au chargement et lors du redimensionnement
        $(document).ready(initMenuScripts);
        $(window).on('resize', function() {
          initMenuScripts();
        });
        

        //link Active
        document.querySelectorAll('.header__bottom nav ul > li:has(ul)').forEach(item => {
          item.addEventListener('mouseover', function() {
              this.classList.add('active');
          });
          item.addEventListener('mouseout', function() {
              this.classList.remove('active');
          });
        });
        $(function() {
          // Récupérer la valeur du paramètre dans l'URL
          var params = new URLSearchParams(window.location.search);
          var secteurId = params.get('field_secteur_target_id');
          
          if (secteurId) {
            $('body').addClass('suppFilter');
          }
          var paramsId = new URLSearchParams(window.location.search);

          // Chercher tous les paramètres commençant par 'field_secteur_target_id['
          if (paramsId.has('field_secteur_target_id[0]')) {
            $('body').addClass('suppSecteur');
          }
        });
      
    },
    _search: function _search() {
      setTimeout(function () {
        let currentUrl = window.location.href;
        let $langueSwitcher = $('.language-switcher-language-url ul.langue-switcher');
      
        // Mettre à jour l'ordre et la classe active
        $langueSwitcher.find('li').each(function () {
          let $link = $(this).find('a');
          if (currentUrl.includes($link.attr('href'))) {
            $langueSwitcher.prepend($(this)).find('li').removeClass('is-active').eq(0).addClass('is-active');
          }
        });
      
        // **Desktop - Hover**
        if (window.innerWidth >= 992) {
          $langueSwitcher.on('mouseenter', function () {
            $(this).addClass('list__is-visible').find('li:not(:first-child)').show();
          });
      
          $langueSwitcher.on('mouseleave', function () {
            $(this).removeClass('list__is-visible').find('li:not(:first-child)').hide();
          });
        }
      
        // **Mobile - Click**
        if (window.innerWidth < 992) {
          $langueSwitcher.on('click', function (e) {
            e.stopPropagation(); // Empêche la propagation du clic
            if ($(this).hasClass('list__is-visible')) {
              // Fermer le menu avec un effet rapide
              $(this).removeClass('list__is-visible').find('li:not(:first-child)').fadeOut(100); // 100ms pour une fermeture rapide
            } else {
              // Ouvrir le menu avec un effet rapide
              $(this).addClass('list__is-visible').find('li:not(:first-child)').fadeIn(100); // 100ms pour une ouverture rapide
            }
          });
        
          // Fermer si clic en dehors
          $(document).on('click', function () {
            if ($langueSwitcher.hasClass('list__is-visible')) {
              $langueSwitcher.removeClass('list__is-visible').find('li:not(:first-child)').fadeOut(100);
            }
          });
        }
        
      }, 500);
      
    },

    _addscroll: function _addscroll() {
      $(document).on('scroll', function () {
        var scrollTop = $(window).scrollTop();
        var scrollLimit = window.matchMedia("(max-width: 992px)").matches ? 20 : 62;
    
        $("body").toggleClass('window_scroll', scrollTop > scrollLimit);
      });
    },

    //swiper
    _swiper: function _swiper() {
      if ($(".sliderHeader__items").length > 0) {
        var swiper = new Swiper(".sliderHeader__items", {
          direction: 'horizontal',
          slidesPerView: 4,
          spaceBetween: 20,
          // initialSlide: 0,
          // loop: true,
          autoplay: {
            delay: 2000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          },
          speed: 2500,
          breakpoints: {
              0: { slidesPerView: 2, speed: 2000 },
              601: { slidesPerView: 3 },
              1025: { slidesPerView: 4 }
          },
          
        });
      }

      //Mtl tv
      if ($(".swiper-video").length > 0) {
        var swiper = new Swiper(".swiper-video", {
            direction: 'horizontal',
            slidesPerView: 3,
            spaceBetween: 20,
            initialSlide: 0,
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
              0: { slidesPerView: 1, speed: 2000},
              601: { slidesPerView: 2 },
              769: { slidesPerView: 3 }
          },
        });
      }

      //e-serices
      if ($(".swiper-services").length > 0) {
        var swiper = new Swiper(".swiper-services", {
            direction: 'horizontal',
            slidesPerView: 3,
            spaceBetween: 0,
            centeredSlides: true,
            loop: true,
            initialSlide: 1,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 1300,
            breakpoints: {
                0: { slidesPerView: 1.4, speed: 2500, spaceBetween: 20, centeredSlides: false,},
                601: { slidesPerView: 2, initialSlide: 0, centeredSlides: false},
                1024: { slidesPerView: 3 }
            },
            // on: {
            //   slideChangeTransitionEnd: function () {
            //       $(".swiper-slide").removeClass("animate-slide");
            //       $(".swiper-slide-active").addClass("animate-slide");
            //   }
            // }
        });
      }
     

      //Chiffres cles
      if ($(".chiffre-cles").length > 0) {
        var swiper = new Swiper(".chiffre-cles", {
            direction: 'horizontal',
            slidesPerView: 4,
            spaceBetween: 20,
            loop: false,
            // initialSlide: 0,
            pagination: {
              el: '.swiper-pagination',
              clickable: true,
            },
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
                0: { slidesPerView: 2, spaceBetween: 10, speed: 2500, },
                768: { slidesPerView: 3 },
                1025: { slidesPerView: 4 }
            },
            on: {
              init: function () {
                  setEqualHeight();
              },
              slideChange: function () {
                  setEqualHeight();
              }
            }
        });
        function setEqualHeight() {
          let maxHeight = 0;
          let $slides = $(".chiffre-cles .swiper-slide .picture");
  
          // Réinitialiser les hauteurs
          $slides.css("height", "");
  
          // Trouver la hauteur maximale
          $slides.each(function () {
              let slideHeight = $(this).outerHeight();
              if (slideHeight > maxHeight) {
                  maxHeight = slideHeight;
              }
          });
  
          // Appliquer la hauteur maximale à tous les slides
          $slides.css("height", maxHeight + "px");
        }
  
        $(window).on("resize", setEqualHeight);
      }

      //Organisme
      $(document).ready(function () { 
        if ($(".organisme").length > 0) {
          var swiper = new Swiper(".organisme", {
            direction: 'horizontal',
            slidesPerView: 4,
            spaceBetween: 20,
            loop: false,
            // initialSlide: 0,
            pagination: {
              el: '.swiper-pagination',
              clickable: true,
            },
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
                0: { slidesPerView: 1.3, speed: 2000},
                482: { slidesPerView: 2},
                991: { slidesPerView: 3 },
                1200: { slidesPerView: 4 }
            },
            on: {
                init: function() {
                    setTimeout(function() { sameHeight(); }, 200);
                },
                resize: function() {
                    setTimeout(function() { sameHeight(); }, 200);
                },
                slideChangeTransitionEnd: function() {
                    sameHeight();
                }
              }
          });
          function sameHeight() {
            const pictures = document.querySelectorAll('.organisme .picture');
            let maxHeight = 0;
            pictures.forEach(function(pic) {
                pic.style.height = 'auto';
            });
            pictures.forEach(function(pic) {
                const height = pic.offsetHeight;
                if (height > maxHeight) maxHeight = height;
            });
            pictures.forEach(function(pic) {
                pic.style.height = maxHeight + 'px';
            });
          }
        }
      })

      //Organisme Listing
      if ($(".organisme-listing").length > 0) {
        var isWrapperOrganisme = function isWrapperOrganisme() {
          return window.innerWidth <= 600;
        };
        if (isWrapperOrganisme()) {
          var swiper = new Swiper(".organisme-listing", {
            direction: 'horizontal',
            slidesPerView: 1.4,
            spaceBetween: 20,
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
              0: { slidesPerView: 1.4, speed: 2000},
              602: { slidesPerView: 8 }
            },
            
          });
        }
      }

      //Liens Utiles
      $(document).ready(function () { 
        if ($(".liens-utile").length > 0) {
          var swiper = new Swiper(".liens-utile", {
              direction: 'horizontal',
              slidesPerView: 4,
              spaceBetween: 20,
              loop: false,
              // initialSlide: 0,
              pagination: {
                el: '.swiper-pagination',
                clickable: true,
              },
              autoplay: {
                delay: 2000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              },
              speed: 2500,
              breakpoints: {
                  0: { slidesPerView: 1.5, speed: 2000},
                  482: { slidesPerView: 2 },
                  768: { slidesPerView: 3 },
                  1025: { slidesPerView: 4 }
              },
              on: {
                init: function() {
                    setTimeout(function() { sameHeight(); }, 200);
                },
                resize: function() {
                    setTimeout(function() { sameHeight(); }, 200);
                },
                slideChangeTransitionEnd: function() {
                    sameHeight();
                }
              }
          });
          function sameHeight() {
            const pictures = document.querySelectorAll('.liens-utile .picture');
            let maxHeight = 0;
            pictures.forEach(function(pic) {
                pic.style.height = 'auto';
            });
            pictures.forEach(function(pic) {
                const height = pic.offsetHeight;
                if (height > maxHeight) maxHeight = height;
            });
            pictures.forEach(function(pic) {
                pic.style.height = maxHeight + 'px';
            });
          }
        }
      })
      
      //Projet
      $(document).ready(function () {
        if ($(".projet__items").length > 0) {
          var swiper = new Swiper(".projet__items", {
            slidesPerView: 3,
            spaceBetween: 20,
            loop: true,
            initialSlide: 0,
            pagination: {
              el: '.swiper-pagination',
              clickable: true,
            },
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
                0: { slidesPerView: 1.4, speed: 2000},
                482: { slidesPerView: 2 },
                769: { slidesPerView: 3 }
            },
        });
        }
      });

      //Partenaire
      if ($(".bloc-partenaire .partenaire").length > 0) {
        var swiper = new Swiper(".bloc-partenaire .partenaire", {
            direction: 'horizontal',
            slidesPerView: 4,
            spaceBetween: 20,
            // loop: true,
            initialSlide: 0,
            pagination: {
              el: '.swiper-pagination',
              clickable: true,
            },
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
                0: { slidesPerView: 1.5, speed: 2000},
                482: { slidesPerView: 2 },
                768: { slidesPerView: 3 },
                1025: { slidesPerView: 4 }
            },
        });
      }

      //Secteur
      if ($(".wrapper-trans .swiper-wrapper-trans").length > 0) {
        var swiper = new Swiper(".wrapper-trans .swiper-wrapper-trans", {
          direction: 'horizontal',
          slidesPerView: 3,
          loop: true,
          grid: {
            rows: 2,
          },
          spaceBetween: 15,
          pagination: {
            el: ".swiper-pagination",
            clickable: true,
          },
            autoplay: {
              delay: 2000,
              disableOnInteraction: false,
              pauseOnMouseEnter: true,
            },
            speed: 2500,
            breakpoints: {
              0: {
                slidesPerView: 1.2,
                speed: 2500,
                grid: {
                  rows: 1,
                },
              },
              601: { 
                slidesPerView: 2,
                grid: {
                  rows: 3,
                },
              },
              1092: {
                slidesPerView: 3,
                grid: {
                  rows: 2,
                }
              }
            },
        });
      }

      //Agenda
      let agendaSwiper = null;

      function initAgendaSwiper() {
        const $blocAgenda = $('.itemsWrapper .bloc-agenda');

        if ($(window).width() <= 640) {
          if (!agendaSwiper) {
            // Vérifie si swiper-wrapper existe déjà
            if (!$blocAgenda.find('.swiper-wrapper').length) {
              const $cards = $blocAgenda.find('.card');
              $cards.wrap('<div class="swiper-slide"></div>');
              $cards.parent().wrapAll('<div class="swiper-wrapper"></div>');
              $blocAgenda.find('.swiper-wrapper').wrap('<div class="swiper swiper-bloc-agenda"></div>');
            }

            agendaSwiper = new Swiper('.swiper-bloc-agenda', {
              autoplay: {
                delay: 2000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              },
              speed: 2500,
              slidesPerView: 1.2,
              spaceBetween: 10,
              loop: true,
            });
          }
        } else {
          if (agendaSwiper) {
            agendaSwiper.destroy(true, true);
            agendaSwiper = null;

            // Nettoyer le DOM si nécessaire (optionnel)
            const $bloc = $('.itemsWrapper .bloc-agenda');
            const $cards = $bloc.find('.swiper-slide .card').unwrap(); // enlever .swiper-slide
            $bloc.find('.swiper-wrapper').replaceWith($cards); // enlever swiper-wrapper
            $bloc.find('.swiper-bloc-agenda').children().unwrap(); // enlever swiper
          }
        }
      }

      // Initialisation
      $(document).ready(function () {
        initAgendaSwiper();
      });

      $(window).on('resize', function () {
        initAgendaSwiper();
      });

      
      


      //blocTabs
      $(document).ready(function () {
        var swiperOnglet  = new Swiper(".swiper-onglet", {
          direction: 'vertical',
          spaceBetween: 20,
          slidesPerView: 3,
          freeMode: false,
          // loop: true,
          breakpoints: {
            0: { slidesPerView: 1, direction: 'horizontal', spaceBetween: 30},
            769: { slidesPerView: 3 }
          },
        });
        var swiperContents = new Swiper(".contentsOnglet", {
          spaceBetween: 0,
          effect: 'fade',
          thumbs: {
            swiper: swiperOnglet,
          },
          navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          },
          breakpoints: {
            0: { allowTouchMove: true},
            768: { allowTouchMove: false},
          },
        });
        $('.swiper-onglet .swiper-slide').on('mouseenter', function () {
          const index = $(this).index();
          swiperContents.slideTo(index);
        });
      });
      
      window.addEventListener('resize', () => {
        var swiperOnglet  = new Swiper(".swiper-onglet", {
          direction: 'vertical',
          spaceBetween: 20,
          slidesPerView: 3,
          freeMode: false,
          // loop: true,
          breakpoints: {
            0: { slidesPerView: 1, direction: 'horizontal', spaceBetween: 30},
            769: { slidesPerView: 3 }
          },
        });
        var swiperContents = new Swiper(".contentsOnglet", {
          spaceBetween: 0,
          effect: 'fade',
          thumbs: {
            swiper: swiperOnglet,
          },
          navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          },
          breakpoints: {
            0: { allowTouchMove: true},
            768: { allowTouchMove: false },
          },
        });
      });
      
    },
    _accordion: function _accordion() {
      //Accordion Footer 
      $('.accord-wysiw').parent().each(function() {
        var $parentContainer = $(this);
        var $accordeonCards = $parentContainer.find('.card.accord-wysiw');
        $accordeonCards.find('ul').addClass('menu');
        $accordeonCards.each(function(index) {
          var $card = $(this);
          var $h3 = $card.find('h3');
          var $div = $card.find('div');
          if (index === 0) {
            $h3.addClass('active');
            $div.show();
          } else {
            $h3.removeClass('active');
            $div.hide();
          }
        });
      });
      // $('.faq .views-field-title').parent().each(function() {
      //   var $parentContainer = $(this);
      //   var $accordeonCards = $parentContainer.find('.card.accord-wysiw');
      //   $accordeonCards.find('ul').addClass('menu');
      //   $accordeonCards.each(function(index) {
      //     var $card = $(this);
      //     var $h3 = $card.find('h3');
      //     var $div = $card.find('div');
      //     if (index === 0) {
      //       $h3.addClass('active');
      //       $div.show();
      //     } else {
      //       $h3.removeClass('active');
      //       $div.hide();
      //     }
      //   });
      // });
      $('.footer__middle > ul > li > a, .accord-wysiw > h3').on('click', function (e) {
        e.preventDefault();
        var $this = $(this);
        var $ul = $this.next('ul');
        var $div = $this.next('div');
      
        // ----- FOOTER -----
        if ($ul.length) {
          if ($ul.is(':visible')) {
            $this.removeClass('active');
            $ul.slideUp();
          } else {
            $('.footer__middle > ul > li > ul').slideUp().prev('a').removeClass('active');
            $this.addClass('active');
            $ul.slideDown();
          }
        }
      
        // ----- ACCORDÉON WYSIWYG -----
        if (('.accord-wysiw').length) {
          if ($div.is(':visible')) {
            $this.removeClass('active');
            $div.slideUp();
          } else {
            $('.accord-wysiw div').slideUp().prev('h3').removeClass('active');
            $this.addClass('active');
            $div.slideDown(); 
          }
        }

      });
      $(document).ready(function () {
        var $firstTitle = $('.faq .views-field-title').first();
        var $firstBody = $firstTitle.next('.views-field-body');

        $firstTitle.addClass('active');
        $firstBody.slideDown();
      });
      $('.faq .views-field-title').on('click', function (e) {
        e.preventDefault();
        var $this = $(this);
        var $body = $this.next('.views-field-body');

        if ($body.is(':visible')) {
          $this.removeClass('active');
          $body.slideUp();
        } else {
            $('.faq .views-field-title').removeClass('active');
            $('.faq .views-field-body').slideUp();
            $this.addClass('active');
            $body.slideDown();
        }
      });
      

      $(document).ready(function () {
        $(".e-services__content--tabs .tab").click(function (e) {
          e.preventDefault();
            var index = $(this).index(); // Récupère l'index de l'onglet cliqué
    
            // Désactiver les onglets et les contenus
            $(".e-services__content--tabs .tab").removeClass("active");
            $(".tab-content").removeClass("active").fadeOut(300); // Disparaît avec animation
    
            // Activer l'onglet et le contenu correspondant avec animation
            $(this).addClass("active");
            $(".tab-content").eq(index).fadeIn(300).addClass("active");
        });
      });

      //tabs page presentation secteur
      $(document).ready(function() {
        const $tabButtons = $('.card.tabs-button');
        const $tabLinks = $tabButtons.find('a');
        const $sections = $($tabLinks.map(function() {
          return $(this).attr('href');
        }));

          // 1. Gestion du clic avec votre méthode de scroll
          $tabLinks.on('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
              window.scrollTo({
                top: targetElement.offsetTop - 400, // Votre offset spécifique
                behavior: 'smooth'
              });
            }
          });
          // 2. Gestion du hover
          $tabLinks.on('mouseenter', function() {
            $tabLinks.removeClass('active');
            $(this).addClass('active');
            $(this).parent().addClass('open');
          });

          $tabButtons.on('mouseleave', function() {
            $(this).removeClass('open');
          });
          // Nouveau: Activation au scroll
          $(window).on('scroll', function() {
            const scrollPosition = $(this).scrollTop() + 1; // +1 pour détection précise
            
            $sections.each(function(index) {
              const sectionTop = $(this).offset().top - 400;
              
              // Active quand le haut de la fenêtre atteint le haut de la section
              if (scrollPosition >= sectionTop && 
                  scrollPosition < sectionTop + $(this).outerHeight()) {
                $tabLinks.removeClass('active').eq(index).addClass('active');
                return false; // Sortie anticipée
              }
            });
          }).trigger('scroll');
      });
        
    
    },

    _tabs: function _tabs() {
        // tabbed content
        $('.tabs li:first-child').addClass('active');
        $(".tabs li:not(:first-child)").removeClass("active")
        $(".tab_content").hide();
        $(".tab_content:first").show();

        $("ul.tabs li").click(function() {
          $(".tab_content").hide();
          var activeTab = $(this).attr("rel");  
          $("#"+activeTab).fadeIn();
        
          $("ul.tabs li").removeClass("active");
          $(this).addClass("active");
  
        $(".tab_drawer_heading").removeClass("d_active");
        $(".tab_drawer_heading[rel^='"+activeTab+"']").addClass("d_active");
        
        });
    },
    _video: function() {
      var $body = $("body");
      function toggle_video_modal() {
        $body.on("click", ".js-trigger-video-modal", function (e) {
            e.preventDefault();
            var id = $(this).attr("data-youtube-id");
            var autoplay = "?autoplay=1";
            var related_no = "&rel=0";
            var mute_no = "&mute=0";
            var src = "//www.youtube.com/embed/" + id + autoplay + related_no + mute_no;
            $("#youtube").attr("src", src);
            $body.addClass("show-video-modal noscroll");
        });
    
        function close_video_modal() {
            $body.removeClass("show-video-modal noscroll");
            $("#youtube").attr("src", "");
        }
        $body.on("click", ".close-video-modal", function () {
            close_video_modal();
        }
        );
        $body.on("keyup", function (e) {
            if (e.keyCode == 27) {
                close_video_modal();
            }
        });
        $body.on("click", ".video-modal", function (e) {
          if (!$(e.target).closest(".video-container").length) {
              close_video_modal();
          }
      });
      }
      toggle_video_modal();
    },
    _select: function (){
      /**** Event select ****/
      if (typeof $.trim !== 'function') {
        $.trim = function(value) {
          return typeof value === 'string' ? value.trim() : '';
        };
      }
      if (typeof jQuery.isFunction !== 'function') {
        jQuery.isFunction = function(obj) {
          return typeof obj === 'function';
        };
      }
      if (typeof jQuery.fn.selectric === 'function') {
        jQuery('.form-select').selectric({ nativeOnMobile: false });
      }
      $(document).ajaxComplete(function(){
        if (typeof jQuery.isFunction !== 'function') {
          jQuery.isFunction = function(obj) {
            return typeof obj === 'function';
          };
        }
        if (typeof jQuery.fn.selectric === 'function') {
          jQuery('.form-select').selectric({ nativeOnMobile: false });
        }
          $('.faq .views-field-title').on('click', function (e) {
            e.preventDefault();
            var $this = $(this);
            var $faq = $this.next('.views-field-body');
          
            // ----- FAQ -----
            if ($faq.length) {
              // Fermer tous les autres sauf celui cliqué
              $('.faq .views-field-body').not($faq).slideUp().prev('.views-field-title').removeClass('active');
          
              // Toggle celui cliqué
              if ($faq.is(':visible')) {
                $this.removeClass('active');
                $faq.slideUp();
              } else {
                $this.addClass('active');
                $faq.slideDown();
              }
            }
          });
          // $('form#views-exposed-form-projects-page-1 select').on('change', function () {
          //   $('form#views-exposed-form-projects-page-1 input[type="submit"]').prop('disabled', false);
          // });
          // $('form#views-exposed-form-projects-page-1 input[type="submit"]').on('click', function () {
          //  $('form#views-exposed-form-projects-page-1').submit();
          // });
          // $('form#views-exposed-form-projects-page-1 input[type="radio"]').on('change', function () {
          //   $('form#views-exposed-form-projects-page-1').submit();
          // });

          $('.mtl-tv__items--item h3,.reglementation .card h2.title-reg').each(function() {
            var thisHeight = $(this).height();
            if (thisHeight > maxHeight) {
              maxHeight = thisHeight;
            }
          });

          $('.mtl-tv__items--item h3, .reglementation .card h2.title-reg').height(maxHeight);
      });
    },
    
    _validationsJs: function (){

      // Validation Filter Header
      if (($("#views-exposed-form-recherches-searchapi-page-1").length > 0)) {
        $("#views-exposed-form-recherches-searchapi-page-1").validate({
          rules: {
            search_api_fulltext: {
              required: true,
              minlength: 3
            }
          },
          messages: {
            search_api_fulltext: {
              required: Drupal.t("Ce champ est obligatoire."),
              minlength: Drupal.t("Saisir au moins 3 caractères.")
            }
          }
        });
      }

      $.validator.addMethod("phonenu", function (value, element) {
        if ( /^\d{3}-?\d{3}-?\d{4}$/g.test(value)) {
            return true;
        } else {
            return false;
        };

      }, Drupal.t("Numéro de téléphone invalide."));

      $.validator.addMethod("laxEmail", function(value, element) {
        // allow any non-whitespace characters as the host part
        if ( /^\b[A-Z0-9._%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b$/i.test(value)) {
            return true;
        } else {
            return false;
        };
      }, Drupal.t("S'il vous plaît, mettez une adresse email valide."));

      $.validator.addMethod("time24", function(value, element) {
        if (!/^\d{2}:\d{2}:\d{2}$/.test(value)) return false;
        var parts = value.split(':');
        if (parts[0] > 23 || parts[1] > 59 || parts[2] > 59) return false;
        return true;
    }, "Invalid time format.");

    }
    
  };
  app.init();
})(jQuery);