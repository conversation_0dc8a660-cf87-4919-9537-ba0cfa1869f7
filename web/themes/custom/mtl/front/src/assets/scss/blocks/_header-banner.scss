.bannerHp {
    width: 100%;
    position: relative;
    height: calc(100vh - 120px);
    & > .picture {
        // height: rem(500);
        height: 100%;
        img {
            width: 100%;
            object-fit: cover;
            height: 100%;
            // transition: opacity 5s cubic-bezier(.19,1,.22,1) 0ms,transform 2s cubic-bezier(.215,.61,.355,1) 0ms;
            // transform: scale(1.05);
            // will-change: transform,opacity;
        }
    }
    &:after {
        content: "";
        position: absolute;
        inset: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
    }
    .video-wrapper {
        position: relative;
        width: 100vw;
        // height: calc(100vh - 120px);
        height: 100%;
        overflow: hidden;
        object-fit:cover;
        z-index: 0;
    }
    .video-wrapper video {
        position: absolute;
        // top: 50%;
        // left: calc(50% - 10px);
        // @include start(position, 50%);
        // min-width: 100vw;
        // min-height: 100vh;
        // transform: translate(-50%, -50%);
        inset: 0;
        width: 100%;
        height: 100%;
        object-fit:cover;
        z-index: 1;
        object-position: center;
        @media(max-width: 991px) {
           left: 50%;
        }
    }
    .video-wrapper::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(15,27,92,0.24);
        z-index: 2;
        pointer-events: none;
    }

    &__page-interne {
        position: relative;
        min-height: 400px;
        .picture {
            position: relative;
            &:after {
                content: "";
                position: absolute;
                background: rgba($black, 0.3);
                inset: 0;
                width: 100%;
                height: 100%;
            }
        }
        img {
            width: 100%;
            height: 400px;
            object-fit: cover;
            .page-log &{
                height: 100%;
            }
        }
        &--title {
            position: absolute;
            z-index: 1;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            width: 95%;
        }
        &.bg-flou {
            z-index: 0;
            img {
                filter: blur(6px);
                opacity: .8;
                object-position: 0 -100px;
            }
            &:after {
                content:"";
                position: absolute;
                inset: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(#000, .45);
                z-index: -1;
            }
        }
    }
    @media(max-width:640px) {
        height: calc(100vh - 200px);
        .video-wrapper {
            height: calc(100vh - 200px);
        }
    }

}