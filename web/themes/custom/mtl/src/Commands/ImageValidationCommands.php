<?php

namespace Drupal\mtl\Commands;

use Drush\Commands\DrushCommands;
use <PERSON><PERSON><PERSON>\node\Entity\Node;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\File\FileSystemInterface;
use <PERSON><PERSON>al\Core\Logger\LoggerChannelFactoryInterface;

/**
 * Drush commands for image validation in MTL theme.
 */
class ImageValidationCommands extends DrushCommands {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * The file system service.
   *
   * @var \Drupal\Core\File\FileSystemInterface
   */
  protected $fileSystem;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
   */
  protected $loggerFactory;

  /**
   * Constructs a new ImageValidationCommands object.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\File\FileSystemInterface $file_system
   *   The file system service.
   * @param \Drupal\Core\Logger\LoggerChannelFactoryInterface $logger_factory
   *   The logger factory.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager, FileSystemInterface $file_system, LoggerChannelFactoryInterface $logger_factory) {
    $this->entityTypeManager = $entity_type_manager;
    $this->fileSystem = $file_system;
    $this->loggerFactory = $logger_factory;
  }

  /**
   * Validate images in actualite nodes.
   *
   * @param int $node_id
   *   Optional node ID to validate specific node.
   * @param array $options
   *   Command options.
   *
   * @command mtl:validate-images
   * @aliases mtl-vi
   * @usage mtl:validate-images
   *   Validate all actualite node images
   * @usage mtl:validate-images 123
   *   Validate image for node ID 123
   * @usage mtl:validate-images --fix
   *   Validate and attempt to fix broken image references
   */
  public function validateImages($node_id = NULL, array $options = ['fix' => FALSE, 'verbose' => FALSE]) {
    $fix = $options['fix'];
    $verbose = $options['verbose'];
    
    $this->output()->writeln('Starting image validation for actualite nodes...');
    
    $node_storage = $this->entityTypeManager->getStorage('node');
    
    // Get nodes to validate
    if ($node_id) {
      $node = $node_storage->load($node_id);
      if (!$node || $node->bundle() !== 'actualite') {
        $this->output()->writeln("<error>Node $node_id not found or is not an actualite node.</error>");
        return;
      }
      $nodes = [$node];
    } else {
      $query = $node_storage->getQuery()
        ->condition('type', 'actualite')
        ->condition('status', 1)
        ->accessCheck(FALSE);
      $nids = $query->execute();
      $nodes = $node_storage->loadMultiple($nids);
    }

    $total_nodes = count($nodes);
    $valid_images = 0;
    $broken_images = 0;
    $no_images = 0;
    $fixed_images = 0;

    $this->output()->writeln("Found $total_nodes actualite nodes to validate.");

    foreach ($nodes as $node) {
      $validation = $this->validateNodeImage($node);
      
      if ($validation['is_valid']) {
        $valid_images++;
        if ($verbose) {
          $this->output()->writeln("<info>✓ Node {$node->id()}: Valid image</info>");
        }
      } elseif ($validation['error'] === 'No image media field or field is empty') {
        $no_images++;
        if ($verbose) {
          $this->output()->writeln("<comment>- Node {$node->id()}: No image</comment>");
        }
      } else {
        $broken_images++;
        $this->output()->writeln("<error>✗ Node {$node->id()}: {$validation['error']}</error>");
        
        if ($fix) {
          if ($this->fixBrokenImage($node)) {
            $fixed_images++;
            $this->output()->writeln("<info>  → Fixed by clearing broken reference</info>");
          }
        }
      }
    }

    // Summary
    $this->output()->writeln('');
    $this->output()->writeln('=== VALIDATION SUMMARY ===');
    $this->output()->writeln("Total nodes: $total_nodes");
    $this->output()->writeln("<info>Valid images: $valid_images</info>");
    $this->output()->writeln("<comment>No images: $no_images</comment>");
    $this->output()->writeln("<error>Broken images: $broken_images</error>");
    
    if ($fix && $fixed_images > 0) {
      $this->output()->writeln("<info>Fixed images: $fixed_images</info>");
    }

    if ($broken_images > 0 && !$fix) {
      $this->output()->writeln('');
      $this->output()->writeln('Run with --fix option to attempt automatic fixes.');
    }
  }

  /**
   * Validates a single node's image.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node to validate.
   *
   * @return array
   *   Validation results.
   */
  protected function validateNodeImage($node) {
    $validation = [
      'is_valid' => FALSE,
      'uri' => NULL,
      'alt' => '',
      'error' => NULL,
    ];

    try {
      // Check if the image media field exists and has a value
      if (!$node->hasField('field_image_media') || $node->get('field_image_media')->isEmpty()) {
        $validation['error'] = 'No image media field or field is empty';
        return $validation;
      }

      // Get the media entity
      $media_entity = $node->get('field_image_media')->entity;
      if (!$media_entity) {
        $validation['error'] = 'Media entity not found';
        return $validation;
      }

      // Check if media entity has the image field
      if (!$media_entity->hasField('field_media_image') || $media_entity->get('field_media_image')->isEmpty()) {
        $validation['error'] = 'Media entity has no image field or field is empty';
        return $validation;
      }

      // Get the file entity
      $file_entity = $media_entity->get('field_media_image')->entity;
      if (!$file_entity) {
        $validation['error'] = 'File entity not found';
        return $validation;
      }

      // Get the file URI
      $file_uri = $file_entity->getFileUri();
      if (!$file_uri) {
        $validation['error'] = 'File URI not found';
        return $validation;
      }

      // Check if the physical file exists
      $file_path = $this->fileSystem->realpath($file_uri);
      
      if (!$file_path || !file_exists($file_path)) {
        $validation['error'] = 'Physical file does not exist: ' . $file_uri;
        return $validation;
      }

      // Check if file is readable
      if (!is_readable($file_path)) {
        $validation['error'] = 'File is not readable: ' . $file_path;
        return $validation;
      }

      // Validate that it's actually an image
      $image_info = getimagesize($file_path);
      if (!$image_info) {
        $validation['error'] = 'File is not a valid image: ' . $file_path;
        return $validation;
      }

      // Get alt text
      $alt_text = '';
      if (!$media_entity->get('field_media_image')->isEmpty()) {
        $alt_text = $media_entity->get('field_media_image')->alt ?: '';
      }

      // If we get here, the image is valid
      $validation['is_valid'] = TRUE;
      $validation['uri'] = $file_uri;
      $validation['alt'] = $alt_text;

    } catch (\Exception $e) {
      $validation['error'] = 'Exception during image validation: ' . $e->getMessage();
    }

    return $validation;
  }

  /**
   * Attempts to fix a broken image reference.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node with broken image.
   *
   * @return bool
   *   TRUE if fixed, FALSE otherwise.
   */
  protected function fixBrokenImage($node) {
    try {
      // Clear the broken image reference
      $node->set('field_image_media', NULL);
      $node->save();
      
      $this->loggerFactory->get('mtl_image_validation')->info('Cleared broken image reference for node @nid', [
        '@nid' => $node->id(),
      ]);
      
      return TRUE;
    } catch (\Exception $e) {
      $this->loggerFactory->get('mtl_image_validation')->error('Failed to fix broken image for node @nid: @error', [
        '@nid' => $node->id(),
        '@error' => $e->getMessage(),
      ]);
      return FALSE;
    }
  }

} 