{#
/**
 * @file
 * Default view template to display all the fields in a row.
 *
 * Available variables:
 * - view: The view in use.
 * - fields: A list of fields, each one contains:
 *   - content: The output of the field.
 *   - raw: The raw data for the field, if it exists. This is NOT output safe.
 *   - class: The safe class ID to use.
 *   - handler: The Views field handler controlling this field.
 *   - inline: Whether or not the field should be inline.
 *   - wrapper_element: An HTML element for a wrapper.
 *   - wrapper_attributes: List of attributes for wrapper element.
 *   - separator: An optional separator that may appear before a field.
 *   - label: The field's label text.
 *   - label_element: An HTML element for a label wrapper.
 *   - label_attributes: List of attributes for label wrapper.
 *   - label_suffix: Colon after the label.
 *   - element_type: An HTML element for the field content.
 *   - element_attributes: List of attributes for HTML element for field content.
 *   - has_label_colon: A boolean indicating whether to display a colon after
 *     the label.
 *   - element_type: An HTML element for the field content.
 *   - element_attributes: List of attributes for HTML element for field content.
 * - row: The raw result from the query, with all data it fetched.
 *
 * @see template_preprocess_views_view_fields()
 *
 * @ingroup themeable
 */
#}

<div class="picture">
  <!-- Image style 404 * 554 -->
  {{ fields.field_image.content|replace({ '<img ': '<img class="animate-img" ' })|raw }}
</div>
<div class="content">
  <h3 class="h2-title white h3-title">{{ fields.title.content }}</h3>
  <div>
    <p>{{ fields.body.content }}</p>
    <a class="btn btn-outline-light bold" href="{{ path('entity.node.canonical', { node: row._entity.id }) }}">
      {{ "Découvrir"|t }}
      <i class="fa-solid fa-arrow-right"></i>
    </a>
  </div>
</div>
<a class="click-me" href="{{ path('entity.node.canonical', { node: row._entity.id }) }}"></a>
