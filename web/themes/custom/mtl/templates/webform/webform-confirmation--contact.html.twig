{#
/**
 * @file
 * Theme implementation for a 'webform' element.
 *
 * This is an copy of the webform.html.twig theme_wrapper which includes the
 * 'title_prefix' and 'title_suffix' variables needed for
 * contextual links to appear.
 *
 * Available variables
 * - attributes: A list of HTML attributes for the wrapper element.
 * - children: The child elements of the webform.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 *
 * @see template_preprocess_webform()
 * @see _webform_form_after_build()
 *
 * @ingroup themeable
 */
#}
<div class="container mt-5 mb-5">
  <div class="card px-4 py-5 p-sm-5">
    <h2 class="h2-title">{{ 'Merci pour votre inscription'|t }}</h2>
    <p>{{ 'Vous allez recevoir un email de confirmation'|t }}</p>
    
    <a href="{{ back_url }}" class="btn btn-outline-success bold">{{ "Retour à la page d'accueil"|t }}</a>
  </div>
</div>
