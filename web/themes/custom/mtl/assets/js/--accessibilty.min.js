(function(a){function b(d){if(c[d])return c[d].exports;var e=c[d]={i:d,l:!1,exports:{}};return a[d].call(e.exports,e,e.exports,b),e.l=!0,e.exports}var c={};return b.m=a,b.c=c,b.d=function(a,c,d){b.o(a,c)||Object.defineProperty(a,c,{enumerable:!0,get:d})},b.r=function(a){'undefined'!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:'Module'}),Object.defineProperty(a,'__esModule',{value:!0})},b.t=function(a,c){if(1&c&&(a=b(a)),8&c)return a;if(4&c&&'object'==typeof a&&a&&a.__esModule)return a;var d=Object.create(null);if(b.r(d),Object.defineProperty(d,'default',{enumerable:!0,value:a}),2&c&&'string'!=typeof a)for(var e in a)b.d(d,e,function(b){return a[b]}.bind(null,e));return d},b.n=function(a){var c=a&&a.__esModule?function(){return a['default']}:function(){return a};return b.d(c,'a',c),c},b.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},b.p='',b(b.s=2)})([function(a){(function(b,c){a.exports=c()})(this,function(){'use strict';function a(a){for(var b,c=1;c<arguments.length;c++)for(var d in b=arguments[c],b)a[d]=b[d];return a}function b(c,d){function e(b,e,f){if('undefined'!=typeof document){f=a({},d,f),'number'==typeof f.expires&&(f.expires=new Date(Date.now()+864e5*f.expires)),f.expires&&(f.expires=f.expires.toUTCString()),b=encodeURIComponent(b).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var g='';for(var h in f)f[h]&&(g+='; '+h,!0!==f[h])&&(g+='='+f[h].split(';')[0]);return document.cookie=b+'='+c.write(e,b)+g}}return Object.create({set:e,get:function(a){if('undefined'!=typeof document&&(!arguments.length||a)){for(var b=document.cookie?document.cookie.split('; '):[],d={},e=0;e<b.length;e++){var f=b[e].split('='),g=f.slice(1).join('=');try{var h=decodeURIComponent(f[0]);if(d[h]=c.read(g,h),a===h)break}catch(a){}}return a?d[a]:d}},remove:function(b,c){e(b,'',a({},c,{expires:-1}))},withAttributes:function(c){return b(this.converter,a({},this.attributes,c))},withConverter:function(c){return b(a({},this.converter,c),this.attributes)}},{attributes:{value:Object.freeze(d)},converter:{value:Object.freeze(c)}})}var c=b({read:function(a){return'"'===a[0]&&(a=a.slice(1,-1)),a.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(a){return encodeURIComponent(a).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:'/'});return c});/*! js-cookie v3.0.5 | MIT */},function(a){a.exports={dataFR:{header:{title:'Options d\'accessibilit\xE9 visuelle'},buttons:{reset:'R\xE9initialiser tout les r\xE9glages d\'accessibilit\xE9',contrast:'Contraste +',screenReader:'Lecteur de texte',highlight:'Liens en surbrillance',biggerText:'Agrandir le texte',spacingText:'Espacer le texte',dyslexia:'Aide Dyslexie',lineHeight:'Hauteur des lignes',cursor:'Curseur',hideImg:'Masquer les images',saturation:'Saturation'},footer:{title:'accessibilité app web - ForNet'}},dataEN:{header:{title:'Visual Accessibility Options'},buttons:{reset:'Reset All Accessibility Settings',contrast:'Contrast +',screenReader:'Screen Reader',highlight:'Highlight Links',biggerText:'Bigger Text',spacingText:'Text Spacing',dyslexia:'Dyslexia Friendly',lineHeight:'Line Height',cursor:'Cursor',hideImg:'Hide Image',saturation:'Saturation'},footer:{title:'accessibilité app web - ForNet'}},dataAR:{header:{title:'\u062E\u064A\u0627\u0631\u0627\u062A \u0627\u0644\u0648\u0644\u0648\u062C\u064A\u0629'},buttons:{reset:'\u0625\u0639\u0627\u062F\u0629 \u0636\u0628\u0637 \u062C\u0645\u064A\u0639 \u062E\u064A\u0627\u0631\u0627\u062A \u0627\u0644\u0648\u0644\u0648\u062C\u064A\u0629 ',contrast:'\u0627\u0644\u062A\u0628\u0627\u064A\u0646 +',screenReader:'\u0642\u0627\u0631\u0626 \u0627\u0644\u0635\u0641\u062D\u0629',highlight:'\u0625\u0646\u0627\u0631\u0629 \u0627\u0644\u0631\u0648\u0627\u0628\u0637',biggerText:'\u062A\u0643\u0628\u064A\u0631 \u0627\u0644\u0646\u0635',spacingText:'\u062A\u0628\u0627\u0639\u062F \u0627\u0644\u0646\u0635',dyslexia:'\u062A\u064A\u0633\u064A\u0631 \u0639\u0633\u0631 \u0627\u0644\u0642\u0631\u0627\u0621\u0629',lineHeight:'\u0625\u0631\u062A\u0641\u0627\u0639 \u0627\u0644\u0633\u0637\u0631',cursor:'\u0645\u0624\u0634\u0631 \u0627\u0644\u0645\u0627\u0648\u0633',hideImg:'\u0625\u062E\u0641\u0627\u0621 \u0627\u0644\u0635\u0648\u0631',saturation:'\u062A\u0634\u0628\u0639 \u0627\u0644\u0623\u0644\u0648\u0627\u0646'},footer:{title:'accessibilité app web - ForNet'}}}},function(a,b,c){a.exports=c(3)},function(a,b,c){'use strict';c.r(b);var d=c(0),e=c.n(d),f=c(1),g=c.t(1,1),h=c(4),i=c.n(h);(function(a){a(document).ready(function(){function b(b){var c=b.parent();c.toggleClass('accessPanel-features__item_active'),c.hasClass('accessPanel-features__item_active')?a(z).appendTo(b):b.find('.accessPanel-features__item__enabled').remove()}function c(a){try{for(var b,c=document.querySelectorAll('body *:not([class*="accessPanel"])'),d=0;d<c.length;d++)b=c[d],b.textContent.trim()&&b.classList.add(a)}catch(a){console.error(a)}}function e(b,c,d){var e=a(b).attr('class'),f=e.split(' ');console.log(f,c.length,d,c[d]),a(b).addClass(c[d]),a(b).removeClass(c[d-1])}function g(a,b,c){c.parent().hasClass('accessPanel-features__item_active')?(d.set(a,b,{expires:7}),console.log('add Cookie')):(d.remove(a),console.log('Remove Cookie !'))}function h(){try{K=0,L=0,M=0,N=0,O=0,P=0,Q=0,a('img').each(function(){a(this).removeClass('hide-image')}),g('accessPanelHideimg',null,C),a('a').each(function(){a(this).removeClass('highlight')}),g('accessPanelHighlightL',null,B),a('.accessPanel-features__item_active > button').each(function(){var c=a(this);b(c),c.find('.accessPanel-features__step').removeClass('active'),c.hasClass(R[0])&&(a('html').removeClass('accessPanel-1-contrast'),g('accessPanelContrast',null,c)),c.hasClass(R[1])&&(a('html').removeClass('accessPanel-1-contrast-dark'),g('accessPanelContrast',null,c)),(c.hasClass(T[0])||c.hasClass(T[1])||c.hasClass(T[2])||c.hasClass(T[3]))&&(a('html').css('font-size',''),g('accessPanelTextB',null,c)),c.hasClass(V[0])&&(a('html').removeClass('accessPanel-spacing-1'),g('accessPanelSpacingText',null,c)),c.hasClass(V[1])&&(a('html').removeClass('accessPanel-spacing-2'),g('accessPanelSpacingText',null,c)),c.hasClass(V[2])&&(a('html').removeClass('accessPanel-spacing-3'),g('accessPanelSpacingText',null,c)),c.hasClass(W[0])&&(a('html').removeClass('accessPanel-LineH-1'),g('accessPanelLineH',null,c)),c.hasClass(W[1])&&(a('html').removeClass('accessPanel-LineH-2'),g('accessPanelLineH',null,c)),c.hasClass(W[2])&&(a('html').removeClass('accessPanel-LineH-3'),g('accessPanelLineH',null,c)),c.hasClass(X[0])&&(a('body').removeClass('accessPanel-1-cursor'),g('accessPanelCursor',null,c)),c.hasClass(X[1])&&(a('#cursorMaskTop').remove(),a('#cursorMaskBottom').remove(),g('accessPanelCursor',null,c)),c.hasClass(X[2])&&(a('#cursorLine').remove(),g('accessPanelCursor',null,c)),(c.hasClass(S[0])||c.hasClass(S[1])||c.hasClass(S[2]))&&(a('html').css('filter',''),g('accessPanelSaturate',null,c)),c.hasClass(U[0])&&(a('.element_contain_text').removeClass('element_contain_text'),g('accessPanelDyslexia',null,c)),c.hasClass(U[1])&&(a('.element_contain_text_arial').removeClass('element_contain_text_arial'),g('accessPanelDyslexia',null,c)),c.attr('class','accessPanel-features__item__i')})}catch(a){console.error(a)}}var i='data'+a('html').attr('lang').toUpperCase()||!1,j=f[i],k=j.header.title,l=j.footer.title,m=j.buttons.contrast,n=j.buttons.screenReader,o=j.buttons.highlight,p=j.buttons.biggerText,q=j.buttons.spacingText,r=j.buttons.dyslexia,s=j.buttons.lineHeight,t=j.buttons.cursor,u=j.buttons.hideImg,v=j.buttons.saturation,w=j.buttons.reset,x=a('#toggleAccessPanel, .accessPanel-header__close'),y=a('#accessPanel'),z='<span class="accessPanel-features__item__enabled"><span style="width: 9px; height: 6px; display: flex;"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 8" width="100%" height="100%"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="m1.5 4.5 2 2 5-5"></path></svg></span></span>',A=a('#screenReader'),B=a('#highlightLinks'),C=a('#hideImages'),D=a('#contrastBg'),E=a('#dyslexiaText'),F=a('#cursorGuide'),G=a('#saturationBg'),H=a('#biggerText'),I=a('#spacingText'),J=a('#lineHeightText'),K=0,L=0,M=0,N=0,O=0,P=0,Q=0,R=['active-1','active-2'],S=['saturationLevelOne','saturationLevelTwo','saturationLevelThree'],T=['textLevelOne','textLevelTwo','textLevelThree','textLevelFor'],U=['dysLevelOne','dysLevelTwo'],V=['spacingLevelOne','spacingLevelTwo','spacingLevelThree'],W=['lineHLevelOne','lineHLevelTwo','LineHLevelThree'],X=['largeCursor','guideMaskCursor','guideLineCursor'],Y={hightLightLink:d.get('accessPanelHighlightL')||0,hideImages:d.get('accessPanelHideimg')||0,dyslexia:d.get('accessPanelDyslexia')||0,spaceTxt:d.get('accessPanelSpacingText')||0,lineH:d.get('accessPanelLineH')||0,contrast:d.get('accessPanelContrast')||0,textBigger:d.get('accessPanelTextB')||0,cursor:d.get('accessPanelCursor')||0,saturate:d.get('accessPanelSaturate')||0};console.log(Y),'active'==Y.hightLightLink&&(b(B),a('a').each(function(){a(this).toggleClass('highlight')})),'active'==Y.hideImages&&(b(C),a('img').each(function(){a(this).toggleClass('hide-image')})),'InvertActive'==Y.contrast?(b(D),D.addClass(R[0]),D.find('.accessPanel-features__step:nth-child(1)').addClass('active'),a('html').addClass('accessPanel-1-contrast'),K++):'DarkModActive'==Y.contrast&&(b(D),D.addClass(R[1]),D.find('.accessPanel-features__step').addClass('active'),a('html').addClass('accessPanel-1-contrast-dark'),K=2),'LevelOne'==Y.saturate?(b(G),G.addClass(S[0]),G.find('.accessPanel-features__step:nth-child(1)').addClass('active'),a('html').css('filter','saturate(0.5)'),L++):'LevelTwo'==Y.saturate?(b(G),G.addClass(S[1]),G.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),a('html').css('filter','saturate(3)'),L=2):'LevelThree'==Y.saturate&&(b(G),G.addClass(S[2]),G.find('.accessPanel-features__step').addClass('active'),a('html').css('filter','saturate(0)'),L=3),0!==Y.textBigger&&(b(H),a('html').css('font-size',Y.textBigger+'px'),'17'==Y.textBigger?(H.addClass(T[0]),H.find('.accessPanel-features__step:nth-child(1)').addClass('active'),M++):'19'==Y.textBigger?(H.addClass(T[1]),H.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),M=2):'22'==Y.textBigger?(H.addClass(T[2]),H.find('.accessPanel-features__step:nth-child(-n + 3)').addClass('active'),M=3):'24'==Y.textBigger&&(H.addClass(T[3]),H.find('.accessPanel-features__step').addClass('active'),M=4)),0!==Y.dyslexia&&(b(E),c('element_contain_text'),'dyslexia'==Y.dyslexia?(E.addClass(U[0]),E.find('.accessPanel-features__step:nth-child(1)').addClass('active'),N++):(E.addClass(U[1]),E.find('.accessPanel-features__step').addClass('active'),N=2,a('.element_contain_text').removeClass('element_contain_text').addClass('element_contain_text_arial'))),0!==Y.spaceTxt&&(b(I),'levelOne'==Y.spaceTxt?(I.addClass(V[0]),I.find('.accessPanel-features__step:nth-child(1)').addClass('active'),O++,a('html').addClass('accessPanel-spacing-1')):'levelTwo'==Y.spaceTxt?(I.addClass(V[1]),I.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),O=2,a('html').addClass('accessPanel-spacing-2')):(I.addClass(V[2]),I.find('.accessPanel-features__step').addClass('active'),O=3,a('html').addClass('accessPanel-spacing-3'))),0!==Y.lineH&&(b(J),'levelOne'==Y.lineH?(J.addClass(W[0]),J.find('.accessPanel-features__step:nth-child(1)').addClass('active'),P++,a('html').addClass('accessPanel-LineH-1')):'levelTwo'==Y.lineH?(J.addClass(W[1]),J.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),P=2,a('html').addClass('accessPanel-LineH-2')):(J.addClass(W[2]),J.find('.accessPanel-features__step').addClass('active'),P=3,a('html').addClass('accessPanel-LineH-3'))),0!==Y.cursor&&(b(F),'largeCursor'==Y.cursor?(F.addClass(X[0]),F.find('.accessPanel-features__step:nth-child(1)').addClass('active'),Q++,a('body').addClass('accessPanel-1-cursor')):'guideMask'==Y.cursor?(F.addClass(X[1]),F.find('.accessPanel-features__step:nth-child(-n + 2)').addClass('active'),a('<div id="cursorMaskTop" style="top:480px"></div><div id="cursorMaskBottom" style="top:600px"></div>').appendTo('body'),a(document).on('mousemove',function(b){var c=b.clientY;a('#cursorMaskTop').css('top',c-60),a('#cursorMaskBottom').css('top',c+60)}),Q=2):(F.addClass(X[2]),F.find('.accessPanel-features__step').addClass('active'),Q=3,a('<div id="cursorLine" ></div>').appendTo('body'),a(document).on('mousemove',function(b){var c=b.clientX,d=b.clientY-15;a('#cursorLine').css({left:c,top:d})}))),a('.accessPanel-header__title').html(k),a('.accessPanel-footer__title').html(l),a('.accessPanel-features__reset__text').html(w),A.find('.accessPanel-features__item__name').html(n),B.find('.accessPanel-features__item__name').html(o),C.find('.accessPanel-features__item__name').html(u),D.find('.accessPanel-features__item__name').html(m),E.find('.accessPanel-features__item__name').html(r),F.find('.accessPanel-features__item__name').html(t),G.find('.accessPanel-features__item__name').html(v),H.find('.accessPanel-features__item__name').html(p),I.find('.accessPanel-features__item__name').html(q),J.find('.accessPanel-features__item__name').html(s),x.on('click',function(){y.hasClass('visible')?y.toggleClass('visible'):y.toggleClass('visible')}),a(document).click(function(b){var c=a(b.target);!c.closest('#accessPanel, #toggleAccessPanel').length&&a('#accessPanel').hasClass('visible')&&y.toggleClass('visible')}),A.on('click',function(){b(a(this))}),B.on('click',function(){var c=a(this);b(a(this)),a('a').each(function(){a(this).toggleClass('highlight')}),g('accessPanelHighlightL','active',c)}),C.on('click',function(){var c=a(this);b(c),a('img').each(function(){a(this).toggleClass('hide-image')}),g('accessPanelHideimg','active',c)}),E.on('click',function(){var d=a(this);e(d,U,N),N===U.length?(N=0,b(d),d.find('.accessPanel-features__step').removeClass('active'),a('.element_contain_text_arial').removeClass('element_contain_text_arial'),g('accessPanelDyslexia',null,d)):N++,d.hasClass(U[0])&&(b(d),d.find('.accessPanel-features__step:nth-child(1)').addClass('active'),c('element_contain_text'),g('accessPanelDyslexia','dyslexia',d)),d.hasClass(U[1])&&(d.find('.accessPanel-features__step:nth-child(2)').addClass('active'),a('.element_contain_text').removeClass('element_contain_text').addClass('element_contain_text_arial'),g('accessPanelDyslexia','arial',d))}),D.on('click',function(){var c=a(this);e(c,R,K),K===R.length?(K=0,b(c),c.find('.accessPanel-features__step').removeClass('active'),a('html').removeClass('accessPanel-1-contrast-dark'),g('accessPanelContrast','InvertActive',c)):K++,c.hasClass(R[0])&&(b(c),c.find('.accessPanel-features__step:nth-child(1)').addClass('active'),a('html').addClass('accessPanel-1-contrast'),g('accessPanelContrast','InvertActive',c)),c.hasClass(R[1])&&(c.find('.accessPanel-features__step:nth-child(2)').addClass('active'),a('html').removeClass('accessPanel-1-contrast').addClass('accessPanel-1-contrast-dark'),g('accessPanelContrast','DarkModActive',c))}),F.on('click',function(){var c=a(this);e(c,X,Q),Q===X.length?(Q=0,b(c),c.find('.accessPanel-features__step').removeClass('active'),a('#cursorLine').remove(),g('accessPanelCursor',null,c)):Q++,c.hasClass(X[0])&&(b(c),c.find('.accessPanel-features__step:nth-child(1)').addClass('active'),a('body').addClass('accessPanel-1-cursor'),g('accessPanelCursor','largeCursor',c)),c.hasClass(X[1])&&(c.find('.accessPanel-features__step:nth-child(2)').addClass('active'),a('body').removeClass('accessPanel-1-cursor'),a('<div id="cursorMaskTop" style="top:480px"></div><div id="cursorMaskBottom" style="top:600px"></div>').appendTo('body'),a(document).on('mousemove',function(b){var c=b.clientY;a('#cursorMaskTop').css('top',c-60),a('#cursorMaskBottom').css('top',c+60)}),g('accessPanelCursor','guideMask',c)),c.hasClass(X[2])&&(c.find('.accessPanel-features__step:nth-child(3)').addClass('active'),a('#cursorMaskTop').remove(),a('#cursorMaskBottom').remove(),a('<div id="cursorLine" ></div>').appendTo('body'),a(document).on('mousemove',function(b){var c=b.clientX,d=b.clientY-15;a('#cursorLine').css({left:c,top:d})}),g('accessPanelCursor','guideLine',c))}),G.on('click',function(c){c.preventDefault();var d=a(this);e(d,S,L),L===S.length?(L=0,b(d),d.find('.accessPanel-features__step').removeClass('active'),a('html').css('filter',''),g('accessPanelSaturate',null,d)):L++,d.hasClass(S[0])&&(b(d),d.find('.accessPanel-features__step:nth-child(1)').addClass('active'),a('html').css('filter','saturate(0.5)'),g('accessPanelSaturate','LevelOne',d)),d.hasClass(S[1])&&(d.find('.accessPanel-features__step:nth-child(2)').addClass('active'),a('html').css('filter','saturate(3)'),g('accessPanelSaturate','LevelTwo',d)),d.hasClass(S[2])&&(d.find('.accessPanel-features__step:nth-child(3)').addClass('active'),a('html').css('filter','saturate(0)'),g('accessPanelSaturate','LevelThree',d))}),H.on('click',function(c){c.preventDefault();var d=a(this);e(d,T,M),M===T.length?(M=0,b(d),d.find('.accessPanel-features__step').removeClass('active'),a('html').css('font-size',''),g('accessPanelTextB',null,d)):M++,d.hasClass(T[0])&&(b(d),d.find('.accessPanel-features__step:nth-child(1)').addClass('active'),a('html').css('font-size','17px'),g('accessPanelTextB','17',d)),d.hasClass(T[1])&&(d.find('.accessPanel-features__step:nth-child(2)').addClass('active'),a('html').css('font-size','19px'),g('accessPanelTextB','19',d)),d.hasClass(T[2])&&(d.find('.accessPanel-features__step:nth-child(3)').addClass('active'),a('html').css('font-size','22px'),g('accessPanelTextB','22',d)),d.hasClass(T[3])&&(d.find('.accessPanel-features__step:nth-child(4)').addClass('active'),a('html').css('font-size','24px'),g('accessPanelTextB','24',d))}),I.on('click',function(c){c.preventDefault();var d=a(this);e(d,V,O),O===V.length?(O=0,b(d),d.find('.accessPanel-features__step').removeClass('active'),a('html').removeClass('accessPanel-spacing-3'),g('accessPanelSpacingText',null,d)):O++,d.hasClass(V[0])&&(b(d),d.find('.accessPanel-features__step:nth-child(1)').addClass('active'),a('html').addClass('accessPanel-spacing-1'),g('accessPanelSpacingText','levelOne',d)),d.hasClass(V[1])&&(d.find('.accessPanel-features__step:nth-child(2)').addClass('active'),a('html').removeClass('accessPanel-spacing-1').addClass('accessPanel-spacing-2'),g('accessPanelSpacingText','levelTwo',d)),d.hasClass(V[2])&&(d.find('.accessPanel-features__step:nth-child(3)').addClass('active'),a('html').removeClass('accessPanel-spacing-2').addClass('accessPanel-spacing-3'),g('accessPanelSpacingText','levelThree',d))}),J.on('click',function(c){c.preventDefault();var d=a(this);e(d,W,P),P===W.length?(P=0,b(d),d.find('.accessPanel-features__step').removeClass('active'),g('accessPanelLineH',null,d),a('html').removeClass('accessPanel-LineH-3')):P++,d.hasClass(W[0])&&(b(d),d.find('.accessPanel-features__step:nth-child(1)').addClass('active'),a('html').addClass('accessPanel-LineH-1'),g('accessPanelLineH','levelOne',d)),d.hasClass(W[1])&&(d.find('.accessPanel-features__step:nth-child(2)').addClass('active'),a('html').removeClass('accessPanel-LineH-1').addClass('accessPanel-LineH-2'),g('accessPanelLineH','levelTwo',d)),d.hasClass(W[2])&&(d.find('.accessPanel-features__step:nth-child(3)').addClass('active'),a('html').removeClass('accessPanel-LineH-2').addClass('accessPanel-LineH-3'),g('accessPanelLineH','levelThree',d))}),a('.accessPanel-features__reset').on('click',function(){h()})})})(jQuery)},function(){}]);