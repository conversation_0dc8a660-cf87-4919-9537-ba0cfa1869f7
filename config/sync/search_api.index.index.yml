uuid: 7d149c48-53a1-4d78-bf50-2a34057dadd0
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.body
    - field.storage.node.field_date
    - search_api.server.meilisearch
  module:
    - node
    - search_api_meilisearch
id: index
name: index
description: ''
read_only: false
field_settings:
  body:
    label: Description
    datasource_id: 'entity:node'
    property_path: body
    type: text
    dependencies:
      config:
        - field.storage.node.body
  field_date:
    label: Date
    datasource_id: 'entity:node'
    property_path: field_date
    type: date
    dependencies:
      config:
        - field.storage.node.field_date
  field_image:
    label: Image
    datasource_id: 'entity:node'
    property_path: field_image
    type: integer
    dependencies:
      config:
        - field.storage.node.field_image
  langcode:
    label: Langue
    datasource_id: 'entity:node'
    property_path: langcode
    type: string
    dependencies:
      module:
        - node
  nid:
    label: 'Identifiant (ID)'
    datasource_id: 'entity:node'
    property_path: nid
    type: integer
    dependencies:
      module:
        - node
  title:
    label: Titre
    datasource_id: 'entity:node'
    property_path: title
    type: string
    dependencies:
      module:
        - node
  type:
    label: 'Type de contenu'
    datasource_id: 'entity:node'
    property_path: type
    type: string
    dependencies:
      module:
        - node
  uid:
    label: 'Écrit par'
    datasource_id: 'entity:node'
    property_path: uid
    type: integer
    dependencies:
      module:
        - node
datasource_settings:
  'entity:node':
    bundles:
      default: false
      selected:
        - actualite
        - agenda
        - appel_d_offre
        - communiques_de_presse
        - e_service
        - page
        - projet
        - publication
        - reglementation
    languages:
      default: true
      selected: {  }
processor_settings:
  add_url: {  }
  aggregated_field: {  }
  custom_value: {  }
  entity_type: {  }
  highlight:
    weights:
      postprocess_query: 0
    prefix: '<strong>'
    suffix: '</strong>'
    excerpt: true
    excerpt_always: false
    excerpt_length: 256
    exclude_fields: {  }
    highlight: always
    highlight_partial: false
  html_filter:
    weights:
      preprocess_index: -15
      preprocess_query: -15
    all_fields: false
    fields:
      - body
      - langcode
      - title
      - type
    title: true
    alt: true
    tags:
      b: 2
      h1: 5
      h2: 3
      h3: 2
      strong: 2
  ignorecase:
    weights:
      preprocess_index: -20
      preprocess_query: -20
    all_fields: false
    fields:
      - body
      - langcode
      - title
      - type
  language_with_fallback: {  }
  rendered_item: {  }
  search_api_meilisearch_language_filter:
    weights:
      preprocess_query: 0
  tokenizer:
    weights:
      preprocess_index: -6
      preprocess_query: -6
    all_fields: false
    fields:
      - body
    spaces: ''
    ignored: ._-
    overlap_cjk: 1
    minimum_word_size: '3'
  transliteration:
    weights:
      preprocess_index: -20
      preprocess_query: -20
    all_fields: false
    fields:
      - body
      - langcode
      - title
      - type
tracker_settings:
  default:
    indexing_order: fifo
options:
  cron_limit: 50
  delete_on_fail: true
  index_directly: true
  track_changes_in_references: true
server: meilisearch 