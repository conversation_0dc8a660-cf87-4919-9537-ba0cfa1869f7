uuid: 35c0d85b-d6fc-4513-8481-5855ce520c65
langcode: fr
status: true
dependencies:
  config:
    - field.storage.node.body
    - field.storage.node.field_date
    - search_api.index.index
  module:
    - better_exposed_filters
    - datetime
    - search_api
    - text
id: search
label: Search
module: views
description: ''
tag: ''
base_table: search_api_index_index
base_field: search_api_id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: Recherche
      fields:
        body:
          id: body
          table: search_api_index_index
          field: body
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: null
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 250
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: true
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api_text
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
            filter_type: xss
        field_date:
          id: field_date
          table: search_api_index_index
          field: field_date
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: datetime_default
          settings:
            timezone_override: ''
            format_type: olivero_medium
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        field_image:
          id: field_image
          table: search_api_index_index
          field: field_image
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_link: ''
            image_style: ''
            image_loading:
              attribute: lazy
            svg_attributes:
              width: null
              height: null
            svg_render_as_image: true
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        title:
          id: title
          table: search_api_index_index
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 10
          total_pages: null
          id: 75
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Éléments par page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- Tout -'
            offset: false
            offset_label: Décalage
          quantity: 6
      exposed_form:
        type: bef
        options:
          submit_button: Appliquer
          reset_button: false
          reset_button_label: Réinitialiser
          exposed_sorts_label: 'Trier par'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: true
              autosubmit_exclude_textfield: true
              autosubmit_textfield_delay: 500
              autosubmit_textfield_minimum_length: 3
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              reset_button_always_show: false
            filter:
              search_api_fulltext:
                plugin_id: default
                advanced:
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
              type:
                plugin_id: default
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
      access:
        type: none
        options: {  }
      cache:
        type: search_api_none
        options: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: 'Aucun résultat trouvé'
            format: basic_html
          tokenize: false
      sorts:
        field_date:
          id: field_date
          table: search_api_index_index
          field: field_date
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      arguments:
        nid:
          id: nid
          table: search_api_index_index
          field: nid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: '1'
          summary_options: {  }
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: true
      filters:
        search_api_fulltext:
          id: search_api_fulltext
          table: search_api_index_index
          field: search_api_fulltext
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_fulltext
          operator: or
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: search_api_fulltext_op
            label: Recherche
            description: ''
            use_operator: false
            operator: search_api_fulltext_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: search_api_fulltext
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              content_editor: '0'
              administrator: '0'
            expose_fields: false
            placeholder: ''
            searched_fields_id: search_api_fulltext_searched_fields
            value_maxlength: 128
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          parse_mode: terms
          min_length: null
          fields: {  }
        search_api_language_1:
          id: search_api_language_1
          table: search_api_index_index
          field: search_api_language_1
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_language
          operator: in
          value:
            '***LANGUAGE_language_interface***': '***LANGUAGE_language_interface***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        type:
          id: type
          table: search_api_index_index
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_options
          operator: or
          value:
            actualite: actualite
            agenda: agenda
            appel_d_offre: appel_d_offre
            communiques_de_presse: communiques_de_presse
            e_service: e_service
            projet: projet
            publication: publication
            reglementation: reglementation
          group: 1
          exposed: true
          expose:
            operator_id: type_op
            label: Type
            description: ''
            use_operator: false
            operator: type_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: type
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              content_editor: '0'
              administrator: '0'
            reduce: true
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
      style:
        type: default
      row:
        type: fields
      query:
        type: search_api_query
        options:
          bypass_access: false
          skip_access: false
          preserve_facet_query_args: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
      tags:
        - 'config:field.storage.node.body'
        - 'config:field.storage.node.field_date'
        - 'config:search_api.index.index'
        - 'search_api_list:index'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders: {  }
      path: recherche
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
      tags:
        - 'config:field.storage.node.body'
        - 'config:field.storage.node.field_date'
        - 'config:search_api.index.index'
        - 'search_api_list:index' 