uuid: c3d4e5f6-g7h8-9i0j-1k2l-3m4n5o6p7q8r
langcode: fr
status: true
dependencies:
  config:
    - node.type.partenaires
    - system.menu.admin
    - taxonomy.vocabulary.modes_de_transport
    - user.role.direction_de_la_marine_marchande
    - user.role.direction_des_transports_routier
    - user.role.direction_generale_de_l_aviation_civile
  content:
    - 'taxonomy_term:modes_de_transport:124ac5c0-e87f-4843-8531-7682cc3608e8'
    - 'taxonomy_term:modes_de_transport:8408e2ca-65c8-40c0-adaa-e0fb35cabd4a'
    - 'taxonomy_term:modes_de_transport:ac2cb1b4-8b69-46b9-a917-2f609f0b09cd'
  module:
    - node
    - taxonomy
    - user
id: partenaires_admin
label: 'Partenaires Administration'
module: views
description: "Administration des partenaires pour les différentes directions."
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: 'Par défaut'
    display_plugin: default
    position: 0
    display_options:
      title: 'Partenaires'
      fields:
        node_bulk_form:
          id: node_bulk_form
          table: node
          field: node_bulk_form
          entity_type: node
          plugin_id: node_bulk_form
          label: ''
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        title:
          id: title
          table: node_field_data
          field: title
          entity_type: node
          entity_field: title
          plugin_id: field
          label: Titre
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: string
          settings:
            link_to_entity: true
        name:
          id: name
          table: users_field_data
          field: name
          relationship: uid
          entity_type: user
          entity_field: name
          plugin_id: field
          label: Auteur
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: string
          settings:
            link_to_entity: true
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: field
          label: Statut
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: boolean
          settings:
            format: default
            format_custom_false: 'Non publié'
            format_custom_true: Publié
        changed:
          id: changed
          table: node_field_data
          field: changed
          entity_type: node
          entity_field: changed
          plugin_id: field
          label: 'Mis à jour'
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: timestamp
          settings:
            date_format: short
            custom_date_format: ''
            timezone: ''
        operations:
          id: operations
          table: node
          field: operations
          entity_type: node
          plugin_id: entity_operations
          label: Opérations
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 50
          total_pages: null
          id: 0
          tags:
            next: 'Suivant ›'
            previous: '‹ Précédent'
            first: '« Premier'
            last: 'Dernier »'
          expose:
            items_per_page: false
            items_per_page_label: 'Éléments par page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- Tout -'
            offset: false
            offset_label: Décalage
          quantity: 9
      exposed_form:
        type: basic
        options:
          submit_button: Filtrer
          reset_button: true
          reset_button_label: Réinitialiser
          exposed_sorts_label: 'Trier par'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            direction_de_la_marine_marchande: direction_de_la_marine_marchande
            direction_des_transports_routier: direction_des_transports_routier
            direction_generale_de_l_aviation_civile: direction_generale_de_l_aviation_civile
      cache:
        type: tag
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          plugin_id: text_custom
          empty: true
          content: 'Aucun partenaire disponible.'
      sorts:
        changed:
          id: changed
          table: node_field_data
          field: changed
          entity_type: node
          entity_field: changed
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: changed
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: Statut
            description: ''
            use_operator: false
            operator: status_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: status
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: true
          group_info:
            label: 'Statut de publication'
            description: ''
            identifier: status
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1:
                title: Publié
                operator: '='
                value: '1'
              2:
                title: 'Non publié'
                operator: '='
                value: '0'
        type:
          id: type
          table: node_field_data
          field: type
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            partenaires: partenaires
          group: 1
          exposed: false
        title:
          id: title
          table: node_field_data
          field: title
          entity_type: node
          entity_field: title
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: title_op
            label: Titre
            description: ''
            use_operator: false
            operator: title_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: title
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            node_bulk_form: node_bulk_form
            title: title
            name: name
            status: status
            changed: changed
            operations: operations
          default: changed
          info:
            node_bulk_form:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            title:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            name:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-low
            status:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            changed:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-low
            operations:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: true
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        uid:
          id: uid
          table: node_field_data
          field: uid
          entity_type: node
          entity_field: uid
          plugin_id: standard
          required: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.roles
      tags: {  }
  page_1:
    id: page_1
    display_title: 'Page Partenaires'
    display_plugin: page
    position: 1
    display_options:
      access:
        type: role
        options:
          role:
            direction_de_la_marine_marchande: direction_de_la_marine_marchande
            direction_des_transports_routier: direction_des_transports_routier
            direction_generale_de_l_aviation_civile: direction_generale_de_l_aviation_civile
      defaults:
        access: false
      display_description: ''
      display_extenders: {  }
      path: admin/content/partenaires
      menu:
        type: normal
        title: 'Partenaires'
        description: 'Gérer les partenaires'
        weight: 7
        menu_name: admin
        parent: system.admin_content
        context: '0'
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.roles
      tags: {  } 