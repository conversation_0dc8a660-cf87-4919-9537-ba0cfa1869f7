# 🔍 Guide de Diagnostic : Images qui ne s'affichent pas

## 📋 Table des matières
- [Vue d'ensemble](#vue-densemble)
- [Outils de diagnostic](#outils-de-diagnostic)
- [Méthodes de vérification](#méthodes-de-vérification)
- [Problèmes courants](#problèmes-courants)
- [Scripts de test](#scripts-de-test)
- [Checklist de diagnostic](#checklist-de-diagnostic)

---

## 🎯 Vue d'ensemble

Quand une image existe physiquement sur le serveur mais ne s'affiche pas dans le navigateur, plusieurs causes sont possibles :
- Chemin incorrect
- Permissions insuffisantes
- Problème de cache
- Configuration serveur
- Fichier corrompu

---

## 🛠️ Outils de diagnostic

### 1. Outils de développement du navigateur (F12)

#### **Onglet Network**
```
1. Ouvrir F12
2. Aller dans l'onglet "Network" 
3. Recharger la page (Ctrl+F5)
4. Chercher le nom de votre image
5. Vérifier le status code
```

**Status codes à surveiller :**
- ✅ `200 OK` : Image chargée correctement
- ❌ `404 Not Found` : Chemin incorrect
- ❌ `403 Forbidden` : Permissions insuffisantes
- ❌ `500 Internal Server Error` : Erreur serveur

#### **Onglet Console**
Rechercher les erreurs comme :
```
Failed to load resource: the server responded with a status of 404 (Not Found)
GET http://site.com/path/to/image.jpg 404 (Not Found)
```

#### **Onglet Elements**
```
1. Clic droit sur l'image → "Inspecter l'élément"
2. Vérifier l'attribut src de la balise <img>
3. Copier l'URL et la tester dans un nouvel onglet
```

### 2. Test direct de l'URL

```
1. Copier l'URL de l'attribut src
2. Ouvrir un nouvel onglet
3. Coller l'URL complète
4. Si l'image ne s'affiche pas → problème confirmé
```

---

## 🔍 Méthodes de vérification

### 1. Vérification côté serveur

#### **Existence du fichier**
```bash
# Vérifier si le fichier existe
ls -la web/themes/custom/mtl/assets/images/default-actualite.jpg

# Résultat attendu :
-rw-r--r-- 1 <USER> <GROUP> 15234 Dec 15 10:30 default-actualite.jpg
```

#### **Permissions du fichier**
```bash
# Vérifier les permissions
stat web/themes/custom/mtl/assets/images/default-actualite.jpg

# Corriger les permissions si nécessaire
chmod 644 web/themes/custom/mtl/assets/images/default-actualite.jpg
chown www-data:www-data web/themes/custom/mtl/assets/images/default-actualite.jpg
```

#### **Intégrité du fichier image**
```bash
# Vérifier que c'est une image valide
file web/themes/custom/mtl/assets/images/default-actualite.jpg

# Résultat attendu :
default-actualite.jpg: JPEG image data, JFIF standard 1.01
```

### 2. Test avec curl

```bash
# Tester l'accès HTTP
curl -I http://votre-site.com/themes/custom/mtl/assets/images/default-actualite.jpg

# Réponse attendue :
HTTP/1.1 200 OK
Content-Type: image/jpeg
Content-Length: 15234
```

### 3. Diagnostic avec PHP/Drupal

#### **Script de test simple**
```php
<?php
// debug_image.php - À placer dans la racine du projet
$image_path = '/themes/custom/mtl/assets/images/default-actualite.jpg';
$full_path = __DIR__ . '/web' . $image_path;

echo "=== DIAGNOSTIC IMAGE ===\n";
echo "Chemin relatif: " . $image_path . "\n";
echo "Chemin complet: " . $full_path . "\n";
echo "Fichier existe: " . (file_exists($full_path) ? '✅ OUI' : '❌ NON') . "\n";
echo "Lisible: " . (is_readable($full_path) ? '✅ OUI' : '❌ NON') . "\n";

if (file_exists($full_path)) {
    echo "Taille: " . filesize($full_path) . " bytes\n";
    
    $image_info = getimagesize($full_path);
    if ($image_info) {
        echo "Type MIME: " . $image_info['mime'] . "\n";
        echo "Dimensions: " . $image_info[0] . 'x' . $image_info[1] . " pixels\n";
        echo "✅ Image valide\n";
    } else {
        echo "❌ ERREUR: Fichier corrompu ou pas une image\n";
    }
} else {
    echo "❌ Le fichier n'existe pas à cet emplacement\n";
}
?>
```

**Utilisation :**
```bash
php debug_image.php
```

#### **Test avec Drush**
```bash
# Tester la fonction Drupal
vendor/bin/drush php:eval "
\$theme_path = \Drupal::service('extension.list.theme')->getPath('mtl');
\$image_path = '/' . \$theme_path . '/assets/images/default-actualite.jpg';
\$full_path = DRUPAL_ROOT . \$image_path;
echo 'Chemin: ' . \$image_path . PHP_EOL;
echo 'Existe: ' . (file_exists(\$full_path) ? 'OUI' : 'NON') . PHP_EOL;
"
```

### 4. Test JavaScript dans la console

```javascript
// Tester le chargement d'image avec JavaScript
function testImage(url) {
    const img = new Image();
    
    img.onload = function() {
        console.log('✅ Image chargée avec succès:', url);
        console.log('Dimensions:', this.width + 'x' + this.height);
    };
    
    img.onerror = function() {
        console.log('❌ Erreur de chargement:', url);
    };
    
    img.src = url;
}

// Utilisation
testImage('/themes/custom/mtl/assets/images/default-actualite.jpg');
```

---

## ⚠️ Problèmes courants

### 1. Chemin incorrect

#### **❌ Erreurs fréquentes :**
```html
<!-- Slash manquant au début -->
<img src="themes/custom/mtl/assets/images/image.jpg">

<!-- Chemin relatif incorrect -->
<img src="../images/image.jpg">

<!-- Extension incorrecte -->
<img src="/path/to/image.png"> <!-- alors que c'est un .jpg -->
```

#### **✅ Chemin correct :**
```html
<img src="/themes/custom/mtl/assets/images/default-actualite.jpg">
```

### 2. Problème de cache

#### **Cache Drupal :**
```bash
# Vider tous les caches
vendor/bin/drush cache:rebuild

# Vider seulement le cache de rendu
vendor/bin/drush cache:clear render
```

#### **Cache navigateur :**
```
Ctrl + F5 (rechargement forcé)
ou
Ctrl + Shift + R
```

### 3. Permissions insuffisantes

#### **Permissions recommandées :**
```bash
# Fichiers images
chmod 644 *.jpg *.png *.gif *.svg

# Dossiers
chmod 755 assets/ images/

# Propriétaire
chown -R www-data:www-data web/themes/custom/mtl/assets/
```

### 4. Configuration serveur

#### **Apache (.htaccess)**
```apache
# Autoriser l'accès aux images
<FilesMatch "\.(jpg|jpeg|png|gif|svg|webp)$">
    Require all granted
    Header set Cache-Control "public, max-age=31536000"
</FilesMatch>
```

#### **Nginx**
```nginx
# Servir les fichiers statiques
location ~* \.(jpg|jpeg|png|gif|svg|webp)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    try_files $uri =404;
}
```

### 5. Problème de chemin dans Twig

#### **❌ Erreur courante :**
```twig
{% set image_path = 'themes/custom/mtl/assets/images/image.jpg' %}
<img src="{{ image_path }}">
```

#### **✅ Correction :**
```twig
{% set image_path = '/themes/custom/mtl/assets/images/image.jpg' %}
<img src="{{ image_path }}">
```

---

## 📝 Scripts de test

### Script de diagnostic complet

```php
<?php
/**
 * Script de diagnostic complet pour les images
 * Usage: php image_diagnostic.php
 */

class ImageDiagnostic {
    
    private $image_path;
    private $full_path;
    private $web_root;
    
    public function __construct($relative_path) {
        $this->image_path = $relative_path;
        $this->web_root = __DIR__ . '/web';
        $this->full_path = $this->web_root . $relative_path;
    }
    
    public function runDiagnostic() {
        echo "🔍 DIAGNOSTIC IMAGE COMPLET\n";
        echo str_repeat("=", 50) . "\n\n";
        
        $this->checkPath();
        $this->checkFile();
        $this->checkPermissions();
        $this->checkImageValidity();
        $this->checkWebAccess();
        
        echo "\n✅ Diagnostic terminé\n";
    }
    
    private function checkPath() {
        echo "📁 VÉRIFICATION DU CHEMIN\n";
        echo "Chemin relatif: " . $this->image_path . "\n";
        echo "Chemin complet: " . $this->full_path . "\n";
        echo "Web root: " . $this->web_root . "\n\n";
    }
    
    private function checkFile() {
        echo "📄 VÉRIFICATION DU FICHIER\n";
        
        if (file_exists($this->full_path)) {
            echo "✅ Le fichier existe\n";
            echo "Taille: " . $this->formatBytes(filesize($this->full_path)) . "\n";
            echo "Modifié: " . date('Y-m-d H:i:s', filemtime($this->full_path)) . "\n";
        } else {
            echo "❌ Le fichier n'existe pas\n";
            $this->suggestAlternatives();
        }
        echo "\n";
    }
    
    private function checkPermissions() {
        echo "🔐 VÉRIFICATION DES PERMISSIONS\n";
        
        if (file_exists($this->full_path)) {
            $perms = fileperms($this->full_path);
            echo "Permissions: " . substr(sprintf('%o', $perms), -4) . "\n";
            echo "Lisible: " . (is_readable($this->full_path) ? "✅ OUI" : "❌ NON") . "\n";
            echo "Propriétaire: " . posix_getpwuid(fileowner($this->full_path))['name'] . "\n";
        }
        echo "\n";
    }
    
    private function checkImageValidity() {
        echo "🖼️ VÉRIFICATION DE L'IMAGE\n";
        
        if (file_exists($this->full_path)) {
            $image_info = getimagesize($this->full_path);
            
            if ($image_info) {
                echo "✅ Image valide\n";
                echo "Type MIME: " . $image_info['mime'] . "\n";
                echo "Dimensions: " . $image_info[0] . 'x' . $image_info[1] . " pixels\n";
                echo "Canaux: " . ($image_info['channels'] ?? 'N/A') . "\n";
            } else {
                echo "❌ Fichier corrompu ou format non supporté\n";
            }
        }
        echo "\n";
    }
    
    private function checkWebAccess() {
        echo "🌐 VÉRIFICATION DE L'ACCÈS WEB\n";
        
        // Simuler une requête HTTP (basique)
        $url = 'http://localhost' . $this->image_path;
        echo "URL testée: " . $url . "\n";
        
        // Note: En production, utiliser curl pour tester réellement
        echo "💡 Testez manuellement cette URL dans votre navigateur\n";
        echo "\n";
    }
    
    private function suggestAlternatives() {
        $dir = dirname($this->full_path);
        if (is_dir($dir)) {
            echo "📂 Fichiers dans le dossier:\n";
            $files = scandir($dir);
            foreach ($files as $file) {
                if (preg_match('/\.(jpg|jpeg|png|gif|svg)$/i', $file)) {
                    echo "  - " . $file . "\n";
                }
            }
        }
    }
    
    private function formatBytes($size, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB');
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
}

// Utilisation
$diagnostic = new ImageDiagnostic('/themes/custom/mtl/assets/images/default-actualite.jpg');
$diagnostic->runDiagnostic();
?>
```

---

## ✅ Checklist de diagnostic

### Phase 1 : Vérification rapide
- [ ] Ouvrir F12 → Network et recharger la page
- [ ] Vérifier le status code de l'image
- [ ] Inspecter l'élément `<img>` et vérifier l'attribut `src`
- [ ] Tester l'URL directement dans le navigateur

### Phase 2 : Vérification serveur
- [ ] Vérifier l'existence du fichier : `ls -la chemin/vers/image.jpg`
- [ ] Vérifier les permissions : `stat chemin/vers/image.jpg`
- [ ] Tester avec curl : `curl -I http://site.com/path/to/image.jpg`

### Phase 3 : Cache et configuration
- [ ] Vider le cache Drupal : `drush cache:rebuild`
- [ ] Vider le cache navigateur : `Ctrl+F5`
- [ ] Vérifier la configuration serveur (.htaccess/nginx.conf)

### Phase 4 : Diagnostic avancé
- [ ] Utiliser le script de diagnostic PHP
- [ ] Vérifier les logs d'erreur serveur
- [ ] Tester avec JavaScript dans la console

### Phase 5 : Solutions
- [ ] Corriger le chemin si nécessaire
- [ ] Ajuster les permissions : `chmod 644 image.jpg`
- [ ] Régénérer l'image si corrompue
- [ ] Mettre à jour la configuration serveur

---

## 🚀 Conseils de prévention

1. **Toujours utiliser des chemins absolus** commençant par `/`
2. **Vérifier les permissions** lors de l'upload d'images
3. **Tester les images** après chaque déploiement
4. **Monitorer les logs** pour détecter les erreurs 404
5. **Utiliser un système de fallback** pour les images manquantes

---

*Ce guide couvre 99% des problèmes d'affichage d'images. Pour des cas spécifiques, n'hésitez pas à combiner plusieurs méthodes de diagnostic.* 