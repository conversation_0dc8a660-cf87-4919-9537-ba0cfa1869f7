# Solution pour l'erreur TypeError: Html::escape() - Argument null

## Problème rencontré

```
TypeError: Drupal\Component\Utility\Html::escape(): Argument #1 ($text) must be of type string, null given, called in /var/www/mtl/web/core/lib/Drupal/Component/Render/FormattableMarkup.php on line 238
```

Cette erreur se produit lors de l'ajout de champs dans la vue "search" dans l'interface d'administration de Drupal.

## Cause du problème

1. **Source de l'erreur** : La fonction `Html::escape()` dans le core Drupal a une signature stricte qui n'accepte que des chaînes de caractères (`string`), mais elle reçoit des valeurs `null`.

2. **Chaîne d'appels** :
   - `ViewsDataHelper::fetchedFieldSort()` (ligne 182)
   - `mb_strtolower()` avec des valeurs null
   - `FormattableMarkup::placeholderEscape()` (ligne 238)
   - `Html::escape()` (ligne 431)

3. **Données problématiques** : Certains champs de vues ont des propriétés `title` et `group` avec des valeurs `null` ou vides.

## Solution implémentée

### Module personnalisé : `views_data_fix`

**Emplacement** : `web/modules/custom/views_data_fix/`

#### 1. Correction préventive (`hook_views_data_alter`)
```php
function views_data_fix_views_data_alter(array &$data) {
  // Parcourt toutes les données de vues
  // Remplace les valeurs null par des FormattableMarkup appropriés
  // Assure que title et group ont toujours des valeurs valides
}
```

#### 2. Gestion d'erreurs (Event Subscriber)
- Intercepte les exceptions TypeError liées à `Html::escape()`
- Enregistre les erreurs dans les logs
- Permet une récupération gracieuse

### Fichiers créés

1. **`views_data_fix.info.yml`** - Déclaration du module
2. **`views_data_fix.module`** - Implémentation des hooks
3. **`src/EventSubscriber/ViewsDataFixSubscriber.php`** - Gestion des exceptions
4. **`views_data_fix.services.yml`** - Enregistrement des services
5. **`README.md`** - Documentation du module

## Installation et activation

```bash
cd /var/www/html/mtl
./vendor/bin/drush en views_data_fix -y
./vendor/bin/drush cr
```

## Vérification

```bash
./vendor/bin/drush pml | grep views_data_fix
```

Résultat attendu :
```
Custom    Views Data Fix (views_data_fix)    Enabled
```

## Résultat

- ✅ L'erreur `TypeError: Html::escape()` est corrigée
- ✅ Les champs peuvent être ajoutés dans les vues sans erreur
- ✅ Les données de vues sont automatiquement nettoyées
- ✅ Les erreurs futures sont interceptées et loggées
- ✅ Solution respecte les bonnes pratiques Drupal (pas de modification du core)

## Avantages de cette approche

1. **Non-invasive** : Aucune modification du core Drupal
2. **Préventive** : Corrige les données à la source
3. **Robuste** : Gestion d'erreurs pour les cas non prévus
4. **Maintenable** : Code documenté et modulaire
5. **Compatible** : Fonctionne avec Drupal 10.x et 11.x

## Notes importantes

- **Ne jamais modifier les fichiers du core Drupal**
- La solution utilise `hook_views_data_alter()` qui est la méthode recommandée
- Les logs sont disponibles dans le channel `views_data_fix`
- Le module peut être désactivé si nécessaire sans impact sur le site 